<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AboutAssets.txt</key>
		<data>
		87D3//yZmTijel0tM/5kvTDtVRM=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		EuEwxbWjPVJpiw6UZHguI2OraTc=
		</data>
		<key>Resources/MauiInfo.plist</key>
		<data>
		iEtuUGCV6JM3gxS/pO33swhmpp8=
		</data>
		<key>Resources/MonoTouchDebugConfiguration.txt</key>
		<data>
		oowkf1sL6T19nanXUObgmuayWqM=
		</data>
		<key>Resources/OpenSans-Regular.ttf</key>
		<data>
		E7tDjzwBoWNBKyICttcpD2lF/d8=
		</data>
		<key>Resources/OpenSans-Semibold.ttf</key>
		<data>
		5sf/EzXZsYij5woLxld7dbOGWVY=
		</data>
		<key>Resources/appicon.icns</key>
		<data>
		i90ghU/x0dES9mVeschyOSGQjKE=
		</data>
		<key>Resources/dotnet_bot.png</key>
		<data>
		TCPYt8l244J9a2sCFwVUdoBhDdA=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		nsnZE/LubM8lWn5PzK0t5hD6/Qg=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		km7arW01LDKD2LzrEduLpKYf69k=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MonoBundle/CommunityToolkit.Maui.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5x23RgIC5jiqZK/FDvtTMayB5QfFX6YIC2/OVRqBTns=
			</data>
		</dict>
		<key>MonoBundle/CommunityToolkit.Maui.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			grLtkqJGIpQeJFv7pqGWFzYeQdJxWezzb4z2K9vi03o=
			</data>
		</dict>
		<key>MonoBundle/CommunityToolkit.Mvvm.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fX61wGbP6IO0ObEgC/Xcs4ex5TGd0u29qp4jrf5BZFI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.AspNetCore.Cryptography.Internal.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zEVH3+v+9BkNkRTc1JkfIs1OvORC+qBWSd3njlWAFcU=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			POWcSA1J10elKXqrFt6oOmwk62b7fdrpiHTdUv+J1gw=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			GjYgctud+y0FwtvukcvNdpGh14sOV/3l4UUcy8Xz25s=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.CSharp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vdwImkd7vqJVVshNLIHNarDveA8Kj/wpq4uUPFaJH2U=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Data.Sqlite.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			j8yW9kCeG/yVrakA8Aj7Od3dpUlo1gUXk/ja95in/Jc=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.EntityFrameworkCore.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/GTdYaJEJftgFEz4LZTXJW5CAHQ9sZHu5gakpS+izRM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.EntityFrameworkCore.Relational.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tudkWmjNCkg8AmiaPohJtvExZwtrRRZFvTKCyaHO3ns=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.EntityFrameworkCore.Sqlite.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wgPjNqtvlzuB61BL7HmsQWJjyOgkyeVbdixFS2ZJalI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.EntityFrameworkCore.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2xJBEknz9PkttodwfZbbGyQITo/ku1BBBNvu2EajRiM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Caching.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ucafld43UfOtIH+wTxEZRRM9pNQkHPYbNoCaFNayKKI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Caching.Memory.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			s0JNriq8b/GmrTzRbthGjFLukop/qFaMSCHdt9ajREs=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Configuration.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ovYqV4/kMaJrqHm8iqldmsIJ9L3L5iqIU8o3mkf8Q0c=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gznK7PspYDo/dTDMKaH8wIxflj0dsSvPca2kGqxUV04=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.DependencyInjection.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7Enlvx+e8cw6DKMMuKUb9auFndBdTsnpNMy3Bzj+CZE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.DependencyInjection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MNcWxxuoFg/MMhdkOogR8+6onB95YGDvdyr/HVoMmn0=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.DependencyModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			e3dTAv+YitwSq4hy3PXWbBAOvu0MPsFVo0reDsYnGFI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Identity.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QIE32QERfl97mhjDbEmYb2NU04I9p0wxidF/FiXOHxE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Identity.Stores.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			P8t5fGGTpm8C00bGSTUJ+1QIQR4p8QMq20qkkXv0tUM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6fITN1sUjtbAsrDM1nMXORCd3Rki5IXH2h55s02G6iQ=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FyyI7owQaytVxFWdFnB+QS4sA0a9bmKWneXMzKMS/5Y=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/P8GwiwW17np7QoSFbp/MohPAlUHgvoZkI7q6XX4OTk=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Options.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			B8CQoxTMPOKPFOABW/inySyfNhbqOyXOj0oEOiXSEjQ=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4QTkqgXjmXb7jr3kNRZ/OJmPLhdnJUYWRhTqP8NkyM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.MacCatalyst.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eDt4WxjmDThQ4KPiLbMuvDNDZZgBXlwgcucMh9h1s+I=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.MacCatalyst.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			5Y1yFFjtcvauVL2JYX7QuLgbAyF8Ek2Wp37k/7gELtM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Controls.Xaml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/7Ffz0uNlHdMZfs0AbtbHfr2Jop7UUtw07zpHmnLm44=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Controls.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vX+zrND4E6cFMmkZRbJcCOCmSsxvAc+Alid41hFDT3w=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Essentials.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			LgXqPadk0ImJ6UamZWfrBlzuUgTBjzBhqtTHaEz/Hkk=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Graphics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Aej+Gl7csER5b2CI21VOT6kqzZaNbkm/av4pXyDBReI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Cdi5mG0G2VWpYcFWjW9wr4I/KsFiouPpX//SBlCIEtE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualBasic.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			3GGaB1Q/+Ss5foA1p9I2HIw8G79BPfETgei4xuFeTxU=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualBasic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+yKRyDiG3PQEdMkmBo4M0l6LH0es9iYhrlQUWjj1zkI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Win32.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bItVc6+eIUDTrT7GTIdpYC4ilD8yNQMyC9tFl6T7jP8=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Win32.Registry.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZwA9qVsxWgrV4si0riumniiwdoKONoiWYnsN+WBExRE=
			</data>
		</dict>
		<key>MonoBundle/Newtonsoft.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IsZJ91/OW+fHzNqIgEc7Y072ns8z9dGritiSyvR9Wgc=
			</data>
		</dict>
		<key>MonoBundle/PetSitterConnect.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			GiehlPBxX215yk0jnvMpDR0Xl+6SeGofgtsAt81I2Gw=
			</data>
		</dict>
		<key>MonoBundle/PetSitterConnect.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			Uj+VDca3i4Ew5gkqaTfMVsSb6xi+qzt5wHgq1aT8PA4=
			</data>
		</dict>
		<key>MonoBundle/SQLitePCLRaw.batteries_v2.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			CYGx4eJN3xjkLQ9s6noxjnNbh1+Yez4wb1VRGHJrJKc=
			</data>
		</dict>
		<key>MonoBundle/SQLitePCLRaw.core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			28/QTpWu+p7w3K1M0gAJsBjdPSlDyMzz9AWXue2RYbg=
			</data>
		</dict>
		<key>MonoBundle/SQLitePCLRaw.provider.e_sqlite3.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MN1q54mSgfm3FaufjOsdZ7bUyNdqIdqq/CFZVfzzfww=
			</data>
		</dict>
		<key>MonoBundle/System.AppContext.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QACP4Zs5jIWx5g8kIujhUYRRleZILuYQFsgGs4deDuA=
			</data>
		</dict>
		<key>MonoBundle/System.Buffers.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			P5tjoAO63iDh00/Fv3id8BgOppc214fiSMK2wGI7lK0=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Concurrent.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qqW/l5RWFdqjkxuYxh6YIh+HKMtlhRIE79yk3waTj4E=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Immutable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FhF1KCDFyRusmFdrWymu5bjq6yI2TOQmZgj3is/wJ/g=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.NonGeneric.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7LYAVewPrDmq+D7soBl71KzyCnOuJwGYK+8qUdSQcNU=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Specialized.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0CS8uDfp4kpuT//f8NNlmP3UEL2AuSuKb2xRd8PO6SI=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sgofo2Cih8itmnKpe57t8fsHlmsQAz7EelOU7l/MtME=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.Annotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eyE2YuTpxXj+VAc0bmkNZVDZo1THylO9wgNIotvSpPM=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.DataAnnotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8BRL/Q2GDETOFJXHo80EnZvKRS80ynyS+z8MnxE6tPc=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.EventBasedAsync.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HGN4GOY8nJSUQlpHVwslgM1D+GkxRP1gwywd2ydm9Bw=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			79tuOJ+1FYwkg96iplPOx9zNPDtjE3CiOkz/MZhi65c=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.TypeConverter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bgTjX8quKshY8w3jRh28PB4qGBFCTNbgXeBq4wJTHVQ=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9tLV47S1R+CWbzv8YWJd1aRP2mpuYCQeDWlaZVEFjmA=
			</data>
		</dict>
		<key>MonoBundle/System.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bCXgeVBK/+u+orMgkHYTSFErKwyDRXRQ02QENAghcbc=
			</data>
		</dict>
		<key>MonoBundle/System.Console.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6A7u0mYUlyQypOW8N5ZCOazK68CTuHvqudvXI4jHSvs=
			</data>
		</dict>
		<key>MonoBundle/System.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nr4xzJhneu19yL6tkK40pgtZAPOgzj+/TvoVK/YsoRw=
			</data>
		</dict>
		<key>MonoBundle/System.Data.Common.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/m2Z4pb8GlpdZjReOQPqkzxGzFqB9IAtIhUeCr+d4C4=
			</data>
		</dict>
		<key>MonoBundle/System.Data.DataSetExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			C0XJhmL6jMXObthYep0O3ZzuO4X4ANOOB9QTu3gOb4o=
			</data>
		</dict>
		<key>MonoBundle/System.Data.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wJvgXJbfD3G040h8rwkOa2T+TYN2yk4h3em4brlFzVs=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Contracts.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kVjTbcttJaxCHiSLnxAuej1XQUo6lRNW50MFGTYTnm8=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ihvQZ38mGb6FaIikM5Hmyde+ypSfcdF0+sOtyjMKVro=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.DiagnosticSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lv5lM1uUp7IWsVyRmSJ7VCe/QJutyUQejwix3NwLLyk=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.FileVersionInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D30si1ZeLe5OzTCAdHABn6lj49Ydn5OEoeBijuBfmXE=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Process.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			mY9jj6YQEgoC6Huz+/W6j3XT4EyY1rzHymdw5JAdB1E=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.StackTrace.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D0QL6+wm7HxxErYQltUepgmFaN4PqMHgSQ4/q+T4QOo=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.TextWriterTraceListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			J6nvheKztYWCy4d7TWasye+iOl4v719tugRN9wxLnGk=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Tools.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Nu7ViYRfEK+DjaaEnFObBXZgM4CeGad12WbMS1sHW8Q=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.TraceSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			da6P0urNbb8Nry9aLS1nstpy/8PLquf8H9zgsSiwsYU=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Tracing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			j9Q1BXCNg+qLbkvycER5Z40XISgzf2N1qTXW9yhLU0s=
			</data>
		</dict>
		<key>MonoBundle/System.Drawing.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/2/RqhMiQIa/CEZfWs8wcpZjSZu163TMterUQWQspc4=
			</data>
		</dict>
		<key>MonoBundle/System.Drawing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			npwhoRjOrA2H+xGgOB+hhjUwVHEoCOjwy7txqTbCGbE=
			</data>
		</dict>
		<key>MonoBundle/System.Dynamic.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vPmrU+xya3xRG3GaKJF87mpKj3PARzvewG8YUz6bm24=
			</data>
		</dict>
		<key>MonoBundle/System.Formats.Asn1.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yHEJsANaas12t/CbNoqhfGoXwR0Lid1G6Lx3LSb81E4=
			</data>
		</dict>
		<key>MonoBundle/System.Formats.Tar.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			y8tKwJUIIpwnmvw1yoIsDMyOKtTeJthaciQPpE1T6Yk=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.Calendars.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1BEwmwFHf3r9tbFAM8P6w8Q+P/jdM5fhXH8oiv1t1Jo=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sfFlomNm9pYGa0N9UazCwXO7BEZ/KFaEfMGPG0s/xYs=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ugwLSQjGLQz0gMX0/YEi0wK3K4AI+z9msauO4WsKqhA=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.Brotli.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nzAGYKGEXKIdFDcxEYqcrRBo+adZeASUZ5dbKtzyL2Q=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			myCIn1prF98js3CrVe3JBPeAuX596AoFXS4VJ2cP8Gc=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.ZipFile.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			38JVN5s1wcmt/uo7QMyn7Bjrmgc6j/pwpU75xxudNb8=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFWt+0xpY09THpDLO5P0DB2WNERYZf5H0Bpl1oKIlo=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aWhY5lhIZy5HL9rmaitx4hEnVynEac+NIHOXWWLwvK8=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.DriveInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7rL57oq77jcxT4/R7nqsB3YIqDNWElmuXGPOBKhEvTw=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MRw9Y5AOJgjrx8CHLqVHZEgevi7r0h+MRBpgLHH2hHM=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.Watcher.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			JtAlECVn160CuT60D/ZtSdZ/P95Zi/uduSES5erD9+g=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jrKLcXb+UkNXK3klwDeCj+xHidD0Q7ujQbnpJlFHCU4=
			</data>
		</dict>
		<key>MonoBundle/System.IO.IsolatedStorage.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NtR11AJNGNc+a9PsjQiA3S6QR0zJfhynTby/1lJ3x38=
			</data>
		</dict>
		<key>MonoBundle/System.IO.MemoryMappedFiles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BEJ1b5NnbPDwB/iMZBJW/pwIN3qQ0TFv71g4kodimxs=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipelines.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lka8Mr74EyAAY0LYPAOPUPEocxadlYFCB2a704QynIw=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipes.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kdGRhGjuB5qv/qvCpSfPusyzjELoyhE9sMSPy/JmjVA=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipes.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NSZbJJieSg7NLnYjeq0RoCfyNPh6h+JDqlLKHQQ1Z5Q=
			</data>
		</dict>
		<key>MonoBundle/System.IO.UnmanagedMemoryStream.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eIf9WKjApgvbyfjTMxzFW6n1/dzQwBpvbSkSV5aTM+w=
			</data>
		</dict>
		<key>MonoBundle/System.IO.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QkCFnVc5rC+JUjrXjwLVHG6WN9aLxREtUyd/nK9t5es=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Expressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			UN1VzstNB7f3WQnDps4PedtPEXUd1uXJKYRNOoJxBv4=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			uUEEqaiMtZ5IdYaPdN0JVVkl9MZeUGcBVyRKl1XvEbs=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Queryable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			V8g+wQsJQ7XYyNhy5ri/get4gu6RnBV+V3wv8vuLbKQ=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tYtp4guMBpwC5KhJkWJ/l1GJG9Ip1Fr45e7vUGt37vo=
			</data>
		</dict>
		<key>MonoBundle/System.Memory.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZuP+5z/83cFX9cF+NEM9HkeQRkn65atqqSaKmo3Q5TM=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Http.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xoi1TqdW7S20tXr2p2qFjBJRT4q72K4kB7RXPGfA4Rk=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Http.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IvipgrRw8hK9leou2nQjG6NaQzd5T8eg5MurcCzrwO8=
			</data>
		</dict>
		<key>MonoBundle/System.Net.HttpListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rqzjr/TKA/ocWSThB5rbg3VfqIH0pPCSaHawu+bB9MY=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Mail.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			F1L+Fj4q4bnBpbm8E0BtOSjqemuZ7H/6A2219AWGkdI=
			</data>
		</dict>
		<key>MonoBundle/System.Net.NameResolution.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RpWMkp5/cuyes65rqAjfdCSHQr/aNElBVa39BqAK7ws=
			</data>
		</dict>
		<key>MonoBundle/System.Net.NetworkInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RIOltrIQPpsVzLu6+Z2KCvpW8FEFzIJAFdZTn/bKG5Y=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Ping.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9AL/YzGnUJMQKzUUFIzvxb9Ko0ZNdR8jqXoTJ0kPXpk=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yvSVdU1lU9bBsiRxN5wKWAzZnsVUdZ21XjLjU5aYLwQ=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Quic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			O5t3CIpkVP9eGW1rfFI+foVqxPr2ieuM6vg2kFBzua0=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Requests.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TNuUFzBWsw2Ey0yxsxkcrG5nieb8EeOf40eVrAiAr0E=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Ycwsw77uwdD5kIRiZzPSgQLnzYLh9dcfsqgbYLnp3k=
			</data>
		</dict>
		<key>MonoBundle/System.Net.ServicePoint.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			cOEIwOt5D7Ta81wVx2zYPccJaA8GFuKMstVQ38oqzgQ=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Sockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			myd6WJ4SFJP40eRgcqHkjHEM/ygrMITPExFBvNCw5U0=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebClient.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rPwU23X1EfngFPwkEKzaiQIFyQzsp2xkgBongoLBikg=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebHeaderCollection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			z1bOh/N+gPIwMPEpqmcHgE8DahVR4Fy44s7mmXZcPFY=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Pn11hjiVFLqfVG8UE+dPCKSa9oO3zq0f84BlZwNTDyI=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebSockets.Client.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0i+ioh7cOaUdRqFXq16PuwRkDPP4bgqnh7UTXN9Fbgo=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebSockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Nh+KhdXJCEActog07iTU9rbFufJq6QnIORW0C2QxyO0=
			</data>
		</dict>
		<key>MonoBundle/System.Net.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vrKh5vO3PK53iKO0/A6RX2AY7cspXMbXeVkgGfUAo9A=
			</data>
		</dict>
		<key>MonoBundle/System.Numerics.Vectors.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vgCzZVEFnU593/ChADyqpCFH8HkqbHHjwk2onGrDV1M=
			</data>
		</dict>
		<key>MonoBundle/System.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ir+o+6gErYYO0VggDTXTZJlI5DEpPffg6U4wdob3lQ0=
			</data>
		</dict>
		<key>MonoBundle/System.ObjectModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PGkfd0gaKLC3/w2kiBZjiYTH41+Rest8gdoediYAx5M=
			</data>
		</dict>
		<key>MonoBundle/System.Private.CoreLib.aotdata.arm64</key>
		<dict>
			<key>hash2</key>
			<data>
			OQNrUD12TV63QuBc7dA/4R119W/JDn9+wewUUNLsvK8=
			</data>
		</dict>
		<key>MonoBundle/System.Private.CoreLib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			pwxJdCvlobaeIwCXbnKhUz4FKe4O0nYX+7nAze86wGg=
			</data>
		</dict>
		<key>MonoBundle/System.Private.DataContractSerialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PpNfg8CFcqPb9C+alISWPinIzSbZUa21y1OMZxHi7JY=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Uri.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Tq46WwBqh16SJB0znv+OGpKxYyN1rXje116HS9zlTBw=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8tbfE110kgzP6Xp9UhrLhJtozyqoEyBKLAwSbTTsfRw=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			q4IKjmQZvltPf5v3IGLx5ljJdYY1DtLkHn4woZuCBUs=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.DispatchProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QGBOIyUjt3avVvyFGbSRrBqIBa9BRH2rf1ELG0iX+1E=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.ILGeneration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8pVzgvBVdZxZAJyzmFBQaCKJrUdZAE1Bt2D6gKGGHq8=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.Lightweight.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kwTKaQ100VDlqqpQcfcIkPToxUodXRbM3A98xKZPw2w=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eK9R2/kZfJfCm6xN4sCv5vgNsfXVM7NIj9XQar4K2eE=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D2Zc9iBR0PfmBW/hym3FwaLrVpcsofe6WRr03sv3HE0=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Metadata.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sA3ld7awvhA/O0p5lcViJauRtIgYzyYzaNX+KfEGyYw=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HcgRLtM5S01xF1msXnhZfaOmg46ONBsV2l1loHfs/kk=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.TypeExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zhpSTxBdqmwh94RLNKpM4N8/iX7OrXnjdh+FS9QJ1Vs=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gVeuMzeEluen8qpROo+UJe3EG/N2/8XWVEnnBvccakM=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.Reader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rvrFafK7KXq/tD+b8emgB9GlhOKMQIgsanx+k1xWynQ=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.ResourceManager.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			q22+Ez+OdBcpf8W6l/TpwsdwWG0JxWM79oK6mfTLXW0=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.Writer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			LAL7hLQ3oznOCMWQgRAFhgjrmW2iDQE5iGN1wgj2r/Q=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.CompilerServices.Unsafe.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bDjFsBwkMO35lnUYTJwDIVDCLTEiPTvYLsLdkpxmOro=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.CompilerServices.VisualC.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			O6GQOgYGLe2Ia0RUBD1EHxJRzNOTEphvpfflDg2bWJw=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8vydAyaTOS221aZvvSi63P9DYQ1ps8BtghXlvV5aRu8=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Handles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ui+rH1LT4sUeOQyRfeq6j8Zu510rFaRc5Z1Z0DmzwMw=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.JavaScript.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			4MZ9pUS1V9gxUOKMIX5RIiQrP/Fr1giMKmhk0IvUqL8=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.RuntimeInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dff1BZmT0frDlidorYVs1rVM7DtYd1ul24QYCip+mro=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xs1LX3bt5WrTcXW/b1z4B80SgxfiCaNlM9PU+yaFQoU=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Intrinsics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EmREIkK9eR2JXuYU5FpWJN5JYnnAoi/62BcFpp/Ip6A=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Loader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DLKM6R4ueOsMFYfXY1V+LnCZlpjdniw40Y7a3pwhmr8=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QC7CL6KRH5q6IR/vsvWH7FGNjTVERpcVUlNQ0uaeLBk=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Formatters.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Gj10cuJGDhYr/imm5r6Uyhfbmk/WWSRClGlKu6O2Ec=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vAUckr3yFtilSkUiaLJEDBbL/kDisytHW+4NlsQx/c0=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Oo5LZ0UGmfcItgRyKBaOhEu3OcjSF7/Btx9qBqXjl3k=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lBca0sD2qnfDrbjf0Sa8UFa3m8xIWVoaEC4HtdnheaU=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			05ndoT/3EmqaTDFRBQfVOgtbRM1AWapbXZjXlFMchQc=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ztszS2ha+wjKewtTYJX5aPnZ3JQ4FtIJgzR8PXXGABA=
			</data>
		</dict>
		<key>MonoBundle/System.Security.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aBs8/aHLlgbO7CmZcjdgP1Yu45PXdtSqlObzNxmb2NM=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Claims.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dKw5ZSJwh8iremVe2N/IA+/URs60Eaz0iiZ5qgFUULI=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Algorithms.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sOxARNyGmEAn1cx24QGHrjLwdtnPt1hPee3RUeZ56q4=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Cng.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gakPU9/CEvzJA9hMW9+c5rls+vj81oBTFzL4EoMRfMs=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Csp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QtF6+7/kzHJfDKJbNjaW7ngjIreMVkuvsZ7z3jlFFM4=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8M/cqsXLtH/smUVjJ1sxSzJEwdRIjyOBBuR5LS4aEnE=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.OpenSsl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			m8XUNexuRZc8WaczWGcvc/Mfq+7XyHz+r7euWiCgvhY=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Zi6aSEh831/BpccrTCLRpJQPur+mHVxBgcD9KlOptfA=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.X509Certificates.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Gj6C07qf4zsp3EQYIZxWxmrba8VJVAqV8vG4NRpLJ70=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eS/Zp82mnDj8keNhiFPfgS7Hf3efkZAKHm8OrM862Hc=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Principal.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Z00jiu5VBEO3jO7jbkchgj4pt6lKA3UbVu6+ONknl00=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Principal.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Y9F1enC1ler/GzITQ9Ecgb2NYs7vIeM0ozHXJIcWy+c=
			</data>
		</dict>
		<key>MonoBundle/System.Security.SecureString.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wCoSk8TNVrw4mDp6Xzr55r7tH2PqpkRY5WxGC4Zz3QM=
			</data>
		</dict>
		<key>MonoBundle/System.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sqa9/DJ3PN9JKpA0ZSioSqzrZdE+8sY1WcNgRSiQLJE=
			</data>
		</dict>
		<key>MonoBundle/System.ServiceModel.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			XsZawEsvJLJB9YOZRQSZy6y3wuQ6B75CWe4xHPyGO9w=
			</data>
		</dict>
		<key>MonoBundle/System.ServiceProcess.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lS70otgoaw27Kn7YzwGfcrpU/1uL3Fan0BSZB7IdPk8=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.CodePages.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xobrOS0bh93LasfAgmTSb5M7xhOxJfUGtDKtW/GP8bg=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			JMzpXvnl/aBW0EwleORLT1SiDWS62CcbMHVL32j8JaA=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			WMEhN8QD2jxk0lH8NM2xGWEavZUnSvHZFuMpd1/f/JU=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encodings.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7UisCEAzWDT9OvRgao4pna7GRHNNCIhfqUcPVSQ8RV0=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VGDr0qK9TXArAwCvzJDsRfJROLS+vSmJIREnKkFEnec=
			</data>
		</dict>
		<key>MonoBundle/System.Text.RegularExpressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			G6h1wE6kSZ3o8w0Qmm3Jb1MvE+K2DAMkpmxf4rciEzM=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Channels.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/ittOl3Ietawzxc0G28L2cI6aZdEro/G8m2n0d7QsjQ=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Overlapped.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MFx0F0aI6WV7Y58AIBPpBpPhnXBY2/Z87qDDecFtTYM=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Dataflow.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			P+cXW912+7tcqfjeiwS5p4GvYgtRftxfN2N39JT0Gpo=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			o4Hd4tXxKIANpFNGpmkELeBKf2qDTILGAcELE+VCbxg=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TZI7+4yYd3xLK8G4J/WmBXicvXmIhVF+bF5FzKdW27Y=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dI7/GwL1BZipisVNjw6/qN17X3Wbuh47AXkzt6kWEeQ=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Thread.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kIgTAIq9RqwiB93ugSHe1NnxYVm6hSW2RCHgmCq3Bzk=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.ThreadPool.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SJ7W346Z39HL2tjc6kuweXlQGU/9giPEf1v2vVl91Q4=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Timer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			3JiGw79iPFE8ioImStDon3XJZpnHG1cXn4O/vn1hYtw=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zNJ/xrIzKnowys+NoQ1vLYCuRVriMBjR4/jGQxLlyLM=
			</data>
		</dict>
		<key>MonoBundle/System.Transactions.Local.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			egnsgFQ8LY7U1qKUjREXOETBZqkZKX4owYZLJIheyUo=
			</data>
		</dict>
		<key>MonoBundle/System.Transactions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			iXSCzJYll+BmHCkCxsp4OAzCb/H3G9ngOeBf7OuryBk=
			</data>
		</dict>
		<key>MonoBundle/System.ValueTuple.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PbMkEXNFYEVq0hgynVC2sDX0062Fr++WCjgV9HOcHUo=
			</data>
		</dict>
		<key>MonoBundle/System.Web.HttpUtility.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KBriTqOEsajUjqzq42OCMyFWexFqN/Z7n+IX3k11P1U=
			</data>
		</dict>
		<key>MonoBundle/System.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			T6DOCGKXS624Zpg/08EBQujfxrWIKKlxvxRAQ3YMluQ=
			</data>
		</dict>
		<key>MonoBundle/System.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HJgPkVqm8aGasqYOPnFwMjdNwp2aGCq/s5856KvUqUo=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			s8nsF+1/0V1Zwq8kwA2ubenPFaUp6cUUS6iLJhfeakI=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.ReaderWriter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jQiy6jQYYdcXnJ60u99y8LpgSQWamL+4y/K5rlJS+ME=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rsebRwl5+eV8/1MP2VrXmuTRrwCuliZWggU1Len0uBE=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VgSYNbhrR/tnRSbedeegFpLrSfJor42CfWF3EHE3Pcg=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XPath.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kEc2/RCvIWf63y/jILR7uxyJgZTUPb1q8mpnYde3g7k=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XPath.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BT5Dx1tuGF1Hdjppbmlf2H+Mp3fdiaWpi+YYgtrRCYM=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XmlDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			q6u4xhzkSbyK9mJAzOUyefSemEEZFETXNrB9IZLaNwE=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XmlSerializer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DQNWf5ZhsnxZoyEdyx9CTT6XoIMExhQI0twNgqqT5h4=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wonWvBgKj54LEllIEQu+tpUyR6rc86NCX/Q+Nz69nLw=
			</data>
		</dict>
		<key>MonoBundle/System.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			W/vTrjiq2wOuwfonpL27CIa9/DCSXUfPZvPWtxl1SF8=
			</data>
		</dict>
		<key>MonoBundle/WindowsBase.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			m+rdPeZviJNARILsvuxBBX+DFYwhachOMtrzVCMTUa4=
			</data>
		</dict>
		<key>MonoBundle/ar/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xjcFG9VRd4e2FBC+dqaBjITKDqCNxZd21nV/5OZzev8=
			</data>
		</dict>
		<key>MonoBundle/ca/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Vjx3PgJFQglmtkcEcz3eaqxs/ZOAK5NnVSnff49MacM=
			</data>
		</dict>
		<key>MonoBundle/cs/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			iqtQ7DyaRGMlN9ewK7FLnT77g85x7o49i+AhBbcoIrM=
			</data>
		</dict>
		<key>MonoBundle/da/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dIPf6dmCYEnjg6hi2MU7Kp631nWoNi9DpOk5ZI3t80s=
			</data>
		</dict>
		<key>MonoBundle/de/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			iR0kRckhzM/PL59tJL4SnIBShKmO+HpKbIbCKbUssFc=
			</data>
		</dict>
		<key>MonoBundle/el/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rLLsfcVPl4NE/Mjrk31Gvv6kuaeOeCdqKv4STnupRGA=
			</data>
		</dict>
		<key>MonoBundle/es/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TMXPvdveiVzO0Q6k1E7vE7xzifyPfKEqCVdAaiZa6uA=
			</data>
		</dict>
		<key>MonoBundle/fi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PUK7FiMOTvCB9yNJHjKzQJ8lQMdRfXKpGcla1tqJrZA=
			</data>
		</dict>
		<key>MonoBundle/fr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qiBB0dux8PxKNUZKjonRRwb4TXcv7NERbzom7q+SHX8=
			</data>
		</dict>
		<key>MonoBundle/he/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nOvEAOQbRCoZ3q7ao0EsXxXDRY6/YwiYm2gWDx0gNoo=
			</data>
		</dict>
		<key>MonoBundle/hi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZNof05Sk1GLEHIvbBFBgcBQ8DCHdmMfdTOQB60ZnYI4=
			</data>
		</dict>
		<key>MonoBundle/hr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IFQKw5vbcDjeNLOh2qczeOgKHVLjplLWlIc58Qy7Gac=
			</data>
		</dict>
		<key>MonoBundle/hu/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			mPWmXjxzqnfeEqBhtT8yHwIa5NzA8VKwrdRUxGin5VI=
			</data>
		</dict>
		<key>MonoBundle/id/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Vh/e1sw7fFTPIrglHxtBsdFSmFgqAOeF/fYHTRZNqFQ=
			</data>
		</dict>
		<key>MonoBundle/it/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			JgkgG6HP5xmAovkU+39afLKI+ckRUT/iuWxqoF1kndg=
			</data>
		</dict>
		<key>MonoBundle/ja/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1nbJ3MyfkAnDjPfQjyR9gpufuZjwQuexmVtj82PmNLU=
			</data>
		</dict>
		<key>MonoBundle/ko/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jjVl7FKe8ClYLK3nfjqKs/LRqgDTDrzXpIdUebqb1po=
			</data>
		</dict>
		<key>MonoBundle/libe_sqlite3.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			9Lnr0MHCWDpPn6v3oBeCrNQ5vnuUMF18ZpD4oSmXzuM=
			</data>
		</dict>
		<key>MonoBundle/ms/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5juTT080JvkXLEXuYfoq4ZKYqGF8C6Opnkh9QYqlgiQ=
			</data>
		</dict>
		<key>MonoBundle/mscorlib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Fr6TLnEVhFAA0wbzUxyAy22L33cBEUCVJ7hLPNPKK/A=
			</data>
		</dict>
		<key>MonoBundle/nb/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZkSc1DQ2l3V/4VEXjxSRsjRhsyxxabwFAARvNmSp0Z4=
			</data>
		</dict>
		<key>MonoBundle/netstandard.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			g18qq3rSuAv9iFMPxYNv3YBhRRC+LARArYc2juTrDGo=
			</data>
		</dict>
		<key>MonoBundle/nl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			UYhWNRkuqz6c+dRMEX4l6bqd7d0MfsL+nT65S29mWBE=
			</data>
		</dict>
		<key>MonoBundle/pl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YR05Q9Smi+mPeECG08FPJRp0SwJoIvTBQPWkC0q+Slo=
			</data>
		</dict>
		<key>MonoBundle/pt-BR/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0h0vTFLxjEszKUhcvwwsSLELqnGt5WiMne3n+Q6dgjk=
			</data>
		</dict>
		<key>MonoBundle/pt/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2Ngczv4KQecsyVJ6lWAdmnayGLYP7cPpUaecUD+ijrA=
			</data>
		</dict>
		<key>MonoBundle/ro/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bNFy8wwFrvYKC+mv79k649ARW0Jqn2kfcRVUD/7wn/I=
			</data>
		</dict>
		<key>MonoBundle/ru/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			UpmV0xCIezx/xonuzscspBj9oDKXO/i3Kn+SXurCcdQ=
			</data>
		</dict>
		<key>MonoBundle/runtimeconfig.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			9GyIrZgyrYqKV2rkmu45bgXS8J6qraLFrZ/C3o6fnfw=
			</data>
		</dict>
		<key>MonoBundle/sk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rwjXoa5ln0zhHXUQe0OdtCGG28EVqS7fJv0+h+e4l80=
			</data>
		</dict>
		<key>MonoBundle/sv/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1GLuvpeql0qx1EiSSmcidiAD8BlM5bXMn2iRyt+hQj0=
			</data>
		</dict>
		<key>MonoBundle/th/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qPxSO/NpDRAfIibJXbLNW++Oq1oK456K5mwtaX2vAAg=
			</data>
		</dict>
		<key>MonoBundle/tr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			E3kRXmarT5O3g25f24fUQ+i6/G4Hy3X9eGeRdse77Yc=
			</data>
		</dict>
		<key>MonoBundle/uk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EeZHgYKaM2i5uMg1LFi17sV0hdtkx+qPtqPvIzTHlBY=
			</data>
		</dict>
		<key>MonoBundle/vi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VrQCBt+wYNVidy/tOqXH3mL1kb622J43RoCH6zGqork=
			</data>
		</dict>
		<key>MonoBundle/zh-HK/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IBs+ZeEuV2hTdjFz3M/Wv2/sH1PdMsDNqTWadREsa5M=
			</data>
		</dict>
		<key>MonoBundle/zh-Hans/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			OULxmasKE5w0xt2x+u9xY06WHnmX6WCm0rE34Blfyxc=
			</data>
		</dict>
		<key>MonoBundle/zh-Hant/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8h+j8csF9GUj5lguhLOIsYwnEkGvu0qkMOWtF3tF2hg=
			</data>
		</dict>
		<key>Resources/AboutAssets.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			fVnsP1e2L88aqi4aLkmySlc7msxv3FPDQxEKAAgiips=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			DAiYBGf6aLF5ncs3VEHofu0lGEwefyuwlqYEXZWmAy0=
			</data>
		</dict>
		<key>Resources/MauiInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tx0KXqEC+DvcCGyogQTEQWHcCWJYYqH24NtPwhpQEjo=
			</data>
		</dict>
		<key>Resources/MonoTouchDebugConfiguration.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			RS51BO98d+zhJWK1abJOyD5jB4nYJnWJPtL2DZyA/zw=
			</data>
		</dict>
		<key>Resources/OpenSans-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6vmgCNlEbtolyBtGmuantYgNtzA6aeHJJyA81HTjlE4=
			</data>
		</dict>
		<key>Resources/OpenSans-Semibold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			yvihPI4b1mopdS0hU/Lvxi7Wurv7VhlmfVMPkRaVDu0=
			</data>
		</dict>
		<key>Resources/appicon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			PuksySzAJl/9rD6oi1A7fdpthEHAQvhJHwFvLytRMxo=
			</data>
		</dict>
		<key>Resources/dotnet_bot.png</key>
		<dict>
			<key>hash2</key>
			<data>
			p7zGimT/tpOpt6JMjvqTXeLvr7gO+6owAxCI06IQS70=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			WxfuPXP2HzlHzcT6HPpxjHyIjhUetCXyRrIRo7hyF2Q=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			a/Og/prhaP+ksiVrPdm6zyz46YfBdrD0oFdkAveKI18=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
