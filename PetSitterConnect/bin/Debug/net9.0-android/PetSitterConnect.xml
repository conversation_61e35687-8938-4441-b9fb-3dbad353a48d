<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PetSitterConnect</name>
    </assembly>
    <members>
        <member name="T:PetSitterConnect.ViewModels.BaseViewModel">
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.IsBusy">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.isBusy"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.Title">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.title"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.IsRefreshing">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.isRefreshing"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BaseViewModel.refreshCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BaseViewModel.RefreshCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.RefreshCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BaseViewModel.RefreshAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingApplicationsViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.requestId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.PetCareRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.petCareRequest"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.Applications">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.applications"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.HasApplications">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.hasApplications"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.SelectedApplication">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.selectedApplication"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.OnRequestIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.loadApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewSitterProfileCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.acceptApplicationCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.rejectApplicationCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewApplicationDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.messageSitterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewSitterReviewsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.refreshApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingDetailViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.bookingId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.Booking">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.booking"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.IsOwner">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.isOwner"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.IsSitter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.isSitter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanAccept">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canAccept"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanReject">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canReject"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanCancel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canCancel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanComplete">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canComplete"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanReview">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canReview"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.StatusColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.statusColor"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.StatusText">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.statusText"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingDetailViewModel.OnBookingIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.loadBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.acceptBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.rejectBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.cancelBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.completeBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.openChatCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.leaveReviewCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.viewPetDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.contactUserCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingListViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.Bookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.bookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.FilteredBookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.filteredBookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.selectedFilter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ShowAsSitter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.showAsSitter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedBooking">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.selectedBooking"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingListViewModel.OnSelectedFilterChanged(PetSitterConnect.ViewModels.BookingFilter)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.loadBookingsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.applyFilterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilter"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.viewBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.toggleViewModeCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.acceptBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.rejectBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.cancelBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.completeBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.UserPets">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.userPets"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.SelectedPet">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.selectedPet"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Title">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.title"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Description">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.description"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.startDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.EndDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.endDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CareType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.careType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Budget">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.budget"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Location">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.location"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.SpecialInstructions">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.specialInstructions"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.errorMessage"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.OnStartDateChanged(System.DateTime)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.loadUserPetsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.createRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.cancelCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.addPetCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.LoginViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.Email">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.email"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.Password">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.password"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.RememberMe">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.rememberMe"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.errorMessage"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.loginCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.LoginCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.LoginCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.LoginAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.navigateToRegisterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.forgotPasswordCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.requestId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.PetCareRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.petCareRequest"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.CanApply">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.canApply"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.IsOwner">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.isOwner"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.errorMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplicationNotes">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.applicationNotes"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.OnRequestIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.loadRequestDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.applyForRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.editRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.deleteRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.viewApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.contactOwnerCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.shareRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.PetCareRequestListViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.PetCareRequests">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.petCareRequests"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.FilteredRequests">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.filteredRequests"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.searchText"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.selectedCareType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ShowMyRequestsOnly">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.showMyRequestsOnly"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.selectedRequest"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OnSearchTextChanged(System.String)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText"/> is changed.</remarks>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OnSelectedCareTypeChanged(System.Nullable{PetSitterConnect.Models.CareType})">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.loadRequestsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.searchCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.applyFiltersCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFiltersCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFiltersCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFilters"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.createRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.viewRequestDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsAsync(PetSitterConnect.Models.PetCareRequest)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.toggleMyRequestsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.RegisterViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.FirstName">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.firstName"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.LastName">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.lastName"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Email">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.email"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Password">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.password"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.ConfirmPassword">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.confirmPassword"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.UserType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.userType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.PhoneNumber">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.phoneNumber"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Address">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.address"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.City">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.city"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.PostalCode">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.postalCode"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Country">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.country"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.errorMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.AcceptTerms">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.acceptTerms"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.RegisterViewModel.registerCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.RegisterViewModel.RegisterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.RegisterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.RegisterViewModel.RegisterAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.RegisterViewModel.navigateToLoginCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.Resource">
            <summary>
            Android Resource Designer class.
            Exposes the Android Resource designer assembly into the project Namespace.
            </summary>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Button.TextColorTo(Microsoft.Maui.Controls.Button,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_DatePicker.TextColorTo(Microsoft.Maui.Controls.DatePicker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Editor.TextColorTo(Microsoft.Maui.Controls.Editor,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Entry.TextColorTo(Microsoft.Maui.Controls.Entry,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_InputView.TextColorTo(Microsoft.Maui.Controls.InputView,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Label.TextColorTo(Microsoft.Maui.Controls.Label,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Picker.TextColorTo(Microsoft.Maui.Controls.Picker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_RadioButton.TextColorTo(Microsoft.Maui.Controls.RadioButton,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_SearchBar.TextColorTo(Microsoft.Maui.Controls.SearchBar,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_TimePicker.TextColorTo(Microsoft.Maui.Controls.TimePicker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs">
            <summary>
            A helper type providing cached, reusable <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instances
            for all properties generated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsBusy">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsBusy" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Title">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Title" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsRefreshing">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsRefreshing" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RequestId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "RequestId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PetCareRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Applications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Applications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CurrentUser" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasApplications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "HasApplications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedApplication">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedApplication" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "BookingId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Booking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Booking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsOwner">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsOwner" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanAccept">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanAccept" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanReject">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanReject" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanCancel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanCancel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanComplete">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanComplete" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanReview">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanReview" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StatusColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StatusColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StatusText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StatusText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Bookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Bookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FilteredBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedFilter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedFilter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowAsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ShowAsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedBooking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedBooking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedPet">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedPet" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Description">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Description" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StartDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StartDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.EndDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "EndDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Budget">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Budget" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Location">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Location" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SpecialInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SpecialInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ErrorMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Email">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Email" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Password">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Password" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RememberMe">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "RememberMe" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanApply">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanApply" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ApplicationNotes">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ApplicationNotes" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PetCareRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FilteredRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SearchText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SearchText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedCareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedCareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowMyRequestsOnly">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ShowMyRequestsOnly" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FirstName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FirstName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.LastName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "LastName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ConfirmPassword">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ConfirmPassword" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PhoneNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PhoneNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Address">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Address" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.City">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "City" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PostalCode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PostalCode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Country">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Country" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.AcceptTerms">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "AcceptTerms" generated properties.</summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs">
            <summary>
            A helper type providing cached, reusable <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instances
            for all properties generated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsBusy">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsBusy" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Title">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Title" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsRefreshing">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsRefreshing" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RequestId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "RequestId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PetCareRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Applications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Applications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CurrentUser" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasApplications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "HasApplications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedApplication">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedApplication" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "BookingId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Booking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Booking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsOwner">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsOwner" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanAccept">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanAccept" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanReject">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanReject" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanCancel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanCancel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanComplete">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanComplete" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanReview">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanReview" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StatusColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StatusColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StatusText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StatusText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Bookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Bookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FilteredBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedFilter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedFilter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowAsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ShowAsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedBooking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedBooking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedPet">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedPet" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Description">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Description" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StartDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StartDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.EndDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "EndDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Budget">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Budget" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Location">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Location" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SpecialInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SpecialInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ErrorMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Email">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Email" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Password">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Password" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RememberMe">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "RememberMe" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanApply">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanApply" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ApplicationNotes">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ApplicationNotes" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PetCareRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FilteredRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SearchText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SearchText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedCareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedCareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowMyRequestsOnly">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ShowMyRequestsOnly" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FirstName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FirstName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.LastName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "LastName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ConfirmPassword">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ConfirmPassword" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PhoneNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PhoneNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Address">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Address" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.City">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "City" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PostalCode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PostalCode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Country">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Country" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.AcceptTerms">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "AcceptTerms" generated properties.</summary>
        </member>
    </members>
</doc>
