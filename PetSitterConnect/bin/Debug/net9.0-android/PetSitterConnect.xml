<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PetSitterConnect</name>
    </assembly>
    <members>
        <member name="T:PetSitterConnect.ViewModels.AddPetViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Name">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.name"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Type">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.type"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Breed">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.breed"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Age">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.age"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Gender">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.gender"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Weight">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.weight"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Size">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.size"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Description">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.description"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.SpecialNeeds">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.specialNeeds"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.MedicalConditions">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.medicalConditions"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.Medications">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.medications"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.FeedingInstructions">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.feedingInstructions"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.IsVaccinated">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.isVaccinated"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.IsNeutered">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.isNeutered"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.IsFriendlyWithOtherPets">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.isFriendlyWithOtherPets"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.IsFriendlyWithChildren">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.isFriendlyWithChildren"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.AddPetViewModel.errorMessage"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.AddPetViewModel.savePetCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.AddPetViewModel.SavePetCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.SavePetCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.AddPetViewModel.SavePetAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.AddPetViewModel.cancelCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.AddPetViewModel.CancelCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.AddPetViewModel.CancelCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.AddPetViewModel.CancelAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BaseViewModel">
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.IsBusy">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.isBusy"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.Title">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.title"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.IsRefreshing">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BaseViewModel.isRefreshing"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BaseViewModel.refreshCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BaseViewModel.RefreshCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BaseViewModel.RefreshCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BaseViewModel.RefreshAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingApplicationsViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.requestId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.PetCareRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.petCareRequest"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.Applications">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.applications"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.HasApplications">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.hasApplications"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.SelectedApplication">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.selectedApplication"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.OnRequestIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RequestId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.loadApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.LoadApplicationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewSitterProfileCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterProfileAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.acceptApplicationCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.AcceptApplicationAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.rejectApplicationCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RejectApplicationAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewApplicationDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewApplicationDetailsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.messageSitterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.MessageSitterAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.viewSitterReviewsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.ViewSitterReviewsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingApplicationsViewModel.refreshApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingApplicationsViewModel.RefreshApplicationsAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingDetailViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.bookingId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.Booking">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.booking"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.IsOwner">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.isOwner"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.IsSitter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.isSitter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanAccept">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canAccept"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanReject">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canReject"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanCancel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canCancel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanComplete">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canComplete"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CanReview">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.canReview"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.StatusColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.statusColor"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.StatusText">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingDetailViewModel.statusText"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingDetailViewModel.OnBookingIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.BookingId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.loadBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.LoadBookingDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.acceptBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.AcceptBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.rejectBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.RejectBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.cancelBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.CancelBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.completeBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.CompleteBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.openChatCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.OpenChatAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.leaveReviewCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.LeaveReviewAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.viewPetDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.ViewPetDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingDetailViewModel.contactUserCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingDetailViewModel.ContactUserAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.BookingListViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.Bookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.bookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.FilteredBookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.filteredBookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.selectedFilter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ShowAsSitter">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.showAsSitter"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedBooking">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.selectedBooking"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.UserRoleIcon">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.userRoleIcon"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.UserRoleLabel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.userRoleLabel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.UserRoleColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.BookingListViewModel.userRoleColor"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.BookingListViewModel.OnSelectedFilterChanged(PetSitterConnect.ViewModels.BookingFilter)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.SelectedFilter"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.loadBookingsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.LoadBookingsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.applyFilterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ApplyFilter"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.viewBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ViewBookingDetailsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.toggleViewModeCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.ToggleViewModeAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.acceptBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.AcceptBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.rejectBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.RejectBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.cancelBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.CancelBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.BookingListViewModel.completeBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.BookingListViewModel.CompleteBookingAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.CalendarBookingViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.CalendarDays">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.calendarDays"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectedDates">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.selectedDates"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.CurrentMonth">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.currentMonth"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.StartDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.startDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.EndDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.endDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.IsSelectingDateRange">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.isSelectingDateRange"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectionMode">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.selectionMode"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.UserRoleIcon">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.userRoleIcon"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.UserRoleLabel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.userRoleLabel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.UserRoleColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.userRoleColor"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.ShowBookingDetails">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.showBookingDetails"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.DayBookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.dayBookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectedDay">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.selectedDay"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.OnCurrentMonthChanged(System.DateTime)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.CurrentMonth"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.CurrentMonth"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.loadCalendarDataCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.LoadCalendarDataCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.LoadCalendarDataCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.LoadCalendarDataAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.generateCalendarDaysCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.GenerateCalendarDaysCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.GenerateCalendarDaysCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.GenerateCalendarDays"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.selectDateCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectDateCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectDateCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.SelectDateAsync(PetSitterConnect.ViewModels.CalendarDay)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.navigateToCreateBookingCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.NavigateToCreateBookingCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.NavigateToCreateBookingCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.NavigateToCreateBookingAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.viewBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.ViewBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.ViewBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.ViewBookingDetailsAsync(PetSitterConnect.Models.Booking)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.previousMonthCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.PreviousMonthCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.PreviousMonthCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.PreviousMonth"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.nextMonthCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.NextMonthCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.NextMonthCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.NextMonth"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CalendarBookingViewModel.clearSelectionCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.ClearSelectionCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarBookingViewModel.ClearSelectionCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CalendarBookingViewModel.ClearSelection"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.CalendarDay">
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.Date">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.date"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.DayNumber">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.dayNumber"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.IsCurrentMonth">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.isCurrentMonth"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.IsToday">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.isToday"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.IsSelected">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.isSelected"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.IsInRange">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.isInRange"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.IsAvailable">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.isAvailable"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.HasBookings">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.hasBookings"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.BookingCount">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.bookingCount"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CalendarDay.BookingStatus">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CalendarDay.bookingStatus"/>
        </member>
        <member name="T:PetSitterConnect.ViewModels.ChatListViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.Conversations">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.conversations"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.SelectedConversation">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.selectedConversation"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.HasConversations">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.hasConversations"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.UserRoleIcon">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.userRoleIcon"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.UserRoleLabel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.userRoleLabel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.UserRoleColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatListViewModel.userRoleColor"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatListViewModel.loadConversationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatListViewModel.LoadConversationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.LoadConversationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatListViewModel.LoadConversationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatListViewModel.openChatCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatListViewModel.OpenChatCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.OpenChatCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatListViewModel.OpenChatAsync(PetSitterConnect.Services.ChatConversation)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatListViewModel.refreshConversationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatListViewModel.RefreshConversationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.RefreshConversationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatListViewModel.RefreshConversationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatListViewModel.viewBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatListViewModel.ViewBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatListViewModel.ViewBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatListViewModel.ViewBookingDetailsAsync(PetSitterConnect.Services.ChatConversation)"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.ChatViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.BookingId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.bookingId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.Messages">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.messages"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.MessageText">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.messageText"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.Booking">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.booking"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.OtherParticipantName">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.otherParticipantName"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.ChatTitle">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.chatTitle"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.CanSendMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.canSendMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.IsTyping">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.isTyping"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.UserRoleIcon">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.userRoleIcon"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.UserRoleColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.ChatViewModel.userRoleColor"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.ChatViewModel.OnBookingIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.BookingId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.BookingId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatViewModel.loadMessagesCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.LoadMessagesCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.LoadMessagesCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatViewModel.LoadMessagesAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatViewModel.sendMessageCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.SendMessageCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.SendMessageCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatViewModel.SendMessageAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatViewModel.deleteMessageCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.DeleteMessageCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.DeleteMessageCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatViewModel.DeleteMessageAsync(PetSitterConnect.Models.ChatMessage)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatViewModel.viewBookingDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.ViewBookingDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.ViewBookingDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatViewModel.ViewBookingDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.ChatViewModel.refreshMessagesCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.ChatViewModel.RefreshMessagesCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.ChatViewModel.RefreshMessagesCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.ChatViewModel.RefreshMessagesAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.UserPets">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.userPets"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.SelectedPet">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.selectedPet"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Title">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.title"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Description">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.description"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.startDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.EndDate">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.endDate"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CareType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.careType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Budget">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.budget"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.Location">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.location"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.SpecialInstructions">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.specialInstructions"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.errorMessage"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.OnStartDateChanged(System.DateTime)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.StartDate"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.loadUserPetsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.LoadUserPetsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.createRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CreateRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.cancelCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.CancelAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.addPetCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.CreatePetCareRequestViewModel.AddPetAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.LoginViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.Email">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.email"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.Password">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.password"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.RememberMe">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.rememberMe"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.LoginViewModel.errorMessage"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.loginCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.LoginCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.LoginCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.LoginAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.navigateToRegisterCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.NavigateToRegisterAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.LoginViewModel.forgotPasswordCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.LoginViewModel.ForgotPasswordAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.requestId"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.PetCareRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.petCareRequest"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.CanApply">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.canApply"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.IsOwner">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.isOwner"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.errorMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplicationNotes">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.applicationNotes"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.OnRequestIdChanged(System.Int32)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.RequestId"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.loadRequestDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.LoadRequestDetailsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.applyForRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ApplyForRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.editRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.EditRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.deleteRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.DeleteRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.viewApplicationsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ViewApplicationsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.contactOwnerCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ContactOwnerAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.shareRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestDetailViewModel.ShareRequestAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.PetCareRequestListViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.PetCareRequests">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.petCareRequests"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.FilteredRequests">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.filteredRequests"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.searchText"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.selectedCareType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ShowMyRequestsOnly">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.showMyRequestsOnly"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedRequest">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.selectedRequest"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CurrentUser">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.currentUser"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.UserRoleIcon">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.userRoleIcon"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.UserRoleLabel">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.userRoleLabel"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.UserRoleColor">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.userRoleColor"/>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OnSearchTextChanged(System.String)">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchText"/> is changed.</remarks>
        </member>
        <member name="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OnSelectedCareTypeChanged(System.Nullable{PetSitterConnect.Models.CareType})">
            <summary>Executes the logic for when <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType"/> just changed.</summary>
            <param name="value">The new property value that was set.</param>
            <remarks>This method is invoked right after the value of <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SelectedCareType"/> is changed.</remarks>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.loadRequestsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.LoadRequestsAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.searchCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.SearchAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.applyFiltersCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFiltersCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFiltersCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ApplyFilters"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.createRequestCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.CreateRequestAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.openCalendarCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OpenCalendarCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OpenCalendarCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.OpenCalendarAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.viewRequestDetailsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ViewRequestDetailsAsync(PetSitterConnect.Models.PetCareRequest)"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.PetCareRequestListViewModel.toggleMyRequestsCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.PetCareRequestListViewModel.ToggleMyRequestsAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.ViewModels.RegisterViewModel">
            <inheritdoc/>
            <inheritdoc/>
            <inheritdoc/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.FirstName">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.firstName"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.LastName">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.lastName"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Email">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.email"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Password">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.password"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.ConfirmPassword">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.confirmPassword"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.UserType">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.userType"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.PhoneNumber">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.phoneNumber"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Address">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.address"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.City">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.city"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.PostalCode">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.postalCode"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.Country">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.country"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.ErrorMessage">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.errorMessage"/>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.AcceptTerms">
            <inheritdoc cref="F:PetSitterConnect.ViewModels.RegisterViewModel.acceptTerms"/>
        </member>
        <member name="F:PetSitterConnect.ViewModels.RegisterViewModel.registerCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.RegisterViewModel.RegisterCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.RegisterCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.RegisterViewModel.RegisterAsync"/>.</summary>
        </member>
        <member name="F:PetSitterConnect.ViewModels.RegisterViewModel.navigateToLoginCommand">
            <summary>The backing field for <see cref="P:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginCommand"/>.</summary>
        </member>
        <member name="P:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginCommand">
            <summary>Gets an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="M:PetSitterConnect.ViewModels.RegisterViewModel.NavigateToLoginAsync"/>.</summary>
        </member>
        <member name="T:PetSitterConnect.Resource">
            <summary>
            Android Resource Designer class.
            Exposes the Android Resource designer assembly into the project Namespace.
            </summary>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Button.TextColorTo(Microsoft.Maui.Controls.Button,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_DatePicker.TextColorTo(Microsoft.Maui.Controls.DatePicker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Editor.TextColorTo(Microsoft.Maui.Controls.Editor,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Entry.TextColorTo(Microsoft.Maui.Controls.Entry,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_InputView.TextColorTo(Microsoft.Maui.Controls.InputView,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Label.TextColorTo(Microsoft.Maui.Controls.Label,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_Picker.TextColorTo(Microsoft.Maui.Controls.Picker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_RadioButton.TextColorTo(Microsoft.Maui.Controls.RadioButton,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_SearchBar.TextColorTo(Microsoft.Maui.Controls.SearchBar,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="M:Microsoft.Maui.Controls.ColorAnimationExtensions_TimePicker.TextColorTo(Microsoft.Maui.Controls.TimePicker,Microsoft.Maui.Graphics.Color,System.UInt32,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationToken)">
            <summary>
            Animates the TextColor of an <see cref = "T:Microsoft.Maui.ITextStyle"/> to the given color
            </summary>
            <param name = "element"></param>
            <param name = "color">The target color to animate the <see cref = "P:Microsoft.Maui.ITextStyle.TextColor"/> to</param>
            <param name = "rate">The time, in milliseconds, between the frames of the animation</param>
            <param name = "length">The duration, in milliseconds, of the animation</param>
            <param name = "easing">The easing function to be used in the animation</param>
            <param name = "token"><see cref = "T:System.Threading.CancellationToken"/></param>
            <returns>Value indicating if the animation completed successfully or not</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs">
            <summary>
            A helper type providing cached, reusable <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instances
            for all properties generated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Name">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Name" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Type">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Type" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Breed">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Breed" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Age">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Age" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Gender">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Gender" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Weight">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Weight" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Size">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Size" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Description">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Description" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SpecialNeeds">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SpecialNeeds" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.MedicalConditions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "MedicalConditions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Medications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Medications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FeedingInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FeedingInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsVaccinated">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsVaccinated" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsNeutered">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsNeutered" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsFriendlyWithOtherPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsFriendlyWithOtherPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsFriendlyWithChildren">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsFriendlyWithChildren" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ErrorMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsBusy">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsBusy" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Title">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Title" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsRefreshing">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsRefreshing" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RequestId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "RequestId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PetCareRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Applications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Applications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CurrentUser" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasApplications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "HasApplications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedApplication">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedApplication" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "BookingId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Booking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Booking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsOwner">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsOwner" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanAccept">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanAccept" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanReject">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanReject" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanCancel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanCancel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanComplete">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanComplete" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanReview">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanReview" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StatusColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StatusColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StatusText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StatusText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Bookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Bookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FilteredBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedFilter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedFilter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowAsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ShowAsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedBooking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedBooking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserRoleIcon" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleLabel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserRoleLabel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserRoleColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CalendarDays">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CalendarDays" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedDates">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedDates" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentMonth">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CurrentMonth" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StartDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "StartDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.EndDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "EndDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsSelectingDateRange">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsSelectingDateRange" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectionMode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectionMode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowBookingDetails">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ShowBookingDetails" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.DayBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "DayBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedDay">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedDay" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Date">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Date" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.DayNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "DayNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsCurrentMonth">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsCurrentMonth" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsToday">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsToday" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsSelected">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsSelected" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsInRange">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsInRange" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsAvailable">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsAvailable" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "HasBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingCount">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "BookingCount" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingStatus">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "BookingStatus" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Conversations">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Conversations" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedConversation">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedConversation" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasConversations">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "HasConversations" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Messages">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Messages" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.MessageText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "MessageText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.OtherParticipantName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "OtherParticipantName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ChatTitle">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ChatTitle" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanSendMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanSendMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsTyping">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "IsTyping" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedPet">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedPet" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Budget">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Budget" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Location">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Location" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SpecialInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SpecialInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Email">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Email" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Password">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Password" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RememberMe">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "RememberMe" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanApply">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "CanApply" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ApplicationNotes">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ApplicationNotes" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PetCareRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FilteredRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SearchText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SearchText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedCareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedCareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowMyRequestsOnly">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ShowMyRequestsOnly" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "SelectedRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FirstName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "FirstName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.LastName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "LastName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ConfirmPassword">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "ConfirmPassword" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "UserType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PhoneNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PhoneNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Address">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Address" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.City">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "City" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PostalCode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "PostalCode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Country">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "Country" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.AcceptTerms">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance for all "AcceptTerms" generated properties.</summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs">
            <summary>
            A helper type providing cached, reusable <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instances
            for all properties generated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Name">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Name" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Type">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Type" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Breed">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Breed" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Age">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Age" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Gender">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Gender" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Weight">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Weight" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Size">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Size" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Description">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Description" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SpecialNeeds">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SpecialNeeds" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.MedicalConditions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "MedicalConditions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Medications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Medications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FeedingInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FeedingInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsVaccinated">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsVaccinated" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsNeutered">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsNeutered" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsFriendlyWithOtherPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsFriendlyWithOtherPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsFriendlyWithChildren">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsFriendlyWithChildren" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ErrorMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsBusy">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsBusy" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Title">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Title" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsRefreshing">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsRefreshing" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RequestId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "RequestId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PetCareRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Applications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Applications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CurrentUser" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasApplications">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "HasApplications" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedApplication">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedApplication" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingId">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "BookingId" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Booking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Booking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsOwner">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsOwner" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanAccept">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanAccept" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanReject">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanReject" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanCancel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanCancel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanComplete">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanComplete" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanReview">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanReview" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StatusColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StatusColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StatusText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StatusText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Bookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Bookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FilteredBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedFilter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedFilter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowAsSitter">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ShowAsSitter" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedBooking">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedBooking" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserRoleIcon" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleLabel">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserRoleLabel" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserRoleColor" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CalendarDays">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CalendarDays" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedDates">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedDates" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentMonth">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CurrentMonth" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StartDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "StartDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.EndDate">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "EndDate" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsSelectingDateRange">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsSelectingDateRange" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectionMode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectionMode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowBookingDetails">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ShowBookingDetails" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.DayBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "DayBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedDay">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedDay" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Date">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Date" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.DayNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "DayNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsCurrentMonth">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsCurrentMonth" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsToday">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsToday" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsSelected">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsSelected" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsInRange">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsInRange" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsAvailable">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsAvailable" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasBookings">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "HasBookings" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingCount">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "BookingCount" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingStatus">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "BookingStatus" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Conversations">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Conversations" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedConversation">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedConversation" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasConversations">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "HasConversations" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Messages">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Messages" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.MessageText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "MessageText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.OtherParticipantName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "OtherParticipantName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ChatTitle">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ChatTitle" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanSendMessage">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanSendMessage" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsTyping">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "IsTyping" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserPets">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserPets" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedPet">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedPet" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Budget">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Budget" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Location">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Location" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SpecialInstructions">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SpecialInstructions" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Email">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Email" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Password">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Password" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RememberMe">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "RememberMe" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanApply">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "CanApply" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ApplicationNotes">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ApplicationNotes" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PetCareRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredRequests">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FilteredRequests" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SearchText">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SearchText" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedCareType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedCareType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowMyRequestsOnly">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ShowMyRequestsOnly" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedRequest">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "SelectedRequest" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FirstName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "FirstName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.LastName">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "LastName" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ConfirmPassword">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "ConfirmPassword" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserType">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "UserType" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PhoneNumber">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PhoneNumber" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Address">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Address" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.City">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "City" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PostalCode">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "PostalCode" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Country">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "Country" generated properties.</summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.AcceptTerms">
            <summary>The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance for all "AcceptTerms" generated properties.</summary>
        </member>
    </members>
</doc>
