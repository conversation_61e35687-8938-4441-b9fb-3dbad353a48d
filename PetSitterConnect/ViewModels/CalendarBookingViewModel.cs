using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Models;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class CalendarBookingViewModel : BaseViewModel
{
    private readonly IPetCareRequestService _petCareRequestService;
    private readonly IBookingService _bookingService;
    private readonly ISchedulingService _schedulingService;
    private readonly IAuthService _authService;

    public CalendarBookingViewModel(
        IPetCareRequestService petCareRequestService,
        IBookingService bookingService,
        ISchedulingService schedulingService,
        IAuthService authService)
    {
        _petCareRequestService = petCareRequestService;
        _bookingService = bookingService;
        _schedulingService = schedulingService;
        _authService = authService;
        Title = "Calendar Booking";

        CalendarDays = new ObservableCollection<CalendarDay>();
        SelectedDates = new ObservableCollection<DateTime>();
        CurrentMonth = DateTime.Today;
        
        GenerateCalendarDays();
    }

    [ObservableProperty]
    private ObservableCollection<CalendarDay> calendarDays;

    [ObservableProperty]
    private ObservableCollection<DateTime> selectedDates;

    [ObservableProperty]
    private DateTime currentMonth;

    [ObservableProperty]
    private DateTime? startDate;

    [ObservableProperty]
    private DateTime? endDate;

    [ObservableProperty]
    private User? currentUser;

    [ObservableProperty]
    private bool isSelectingDateRange;

    [ObservableProperty]
    private string selectionMode = "Start Date";

    [ObservableProperty]
    private string userRoleIcon = "👤";

    [ObservableProperty]
    private string userRoleLabel = "USER";

    [ObservableProperty]
    private Color userRoleColor = Colors.Gray;

    [ObservableProperty]
    private bool showBookingDetails;

    [ObservableProperty]
    private List<Booking> dayBookings = new();

    [ObservableProperty]
    private DateTime selectedDay;

    public override async Task InitializeAsync()
    {
        CurrentUser = await _authService.GetCurrentUserAsync();
        if (CurrentUser != null)
        {
            UpdateUserRoleDisplay();
            await LoadCalendarDataAsync();
        }
    }

    private void UpdateUserRoleDisplay()
    {
        if (CurrentUser == null) return;

        UserRoleIcon = CurrentUser.UserType switch
        {
            UserType.PetOwner => "🏠",
            UserType.PetSitter => "🐕‍🦺",
            UserType.Both => "🏠🐕‍🦺",
            _ => "👤"
        };

        UserRoleLabel = CurrentUser.UserType switch
        {
            UserType.PetOwner => "PET OWNER",
            UserType.PetSitter => "PET SITTER",
            UserType.Both => "OWNER & SITTER",
            _ => "USER"
        };

        UserRoleColor = CurrentUser.UserType switch
        {
            UserType.PetOwner => Color.FromArgb("#2E7D32"), // Green
            UserType.PetSitter => Color.FromArgb("#1976D2"), // Blue
            UserType.Both => Color.FromArgb("#7B1FA2"), // Purple
            _ => Colors.Gray
        };

        Title = CurrentUser.UserType switch
        {
            UserType.PetOwner => "Select Booking Dates",
            UserType.PetSitter => "View Booking Calendar",
            UserType.Both => "Booking Calendar",
            _ => "Calendar"
        };
    }

    [RelayCommand]
    private async Task LoadCalendarDataAsync()
    {
        await ExecuteAsync(async () =>
        {
            if (CurrentUser == null) return;

            // Load bookings for the current month
            var monthStart = new DateTime(CurrentMonth.Year, CurrentMonth.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);

            var bookings = await _schedulingService.GetBookingsForDateAsync(CurrentUser.Id, monthStart);
            
            // Update calendar days with booking information
            foreach (var day in CalendarDays)
            {
                var dayBookings = bookings.Where(b => 
                    b.PetCareRequest.StartDate.Date <= day.Date && 
                    b.PetCareRequest.EndDate.Date >= day.Date).ToList();

                day.HasBookings = dayBookings.Any();
                day.BookingCount = dayBookings.Count();
                day.BookingStatus = dayBookings.FirstOrDefault()?.Status ?? BookingStatus.Pending;
            }
        });
    }

    [RelayCommand]
    private void GenerateCalendarDays()
    {
        CalendarDays.Clear();

        var firstDayOfMonth = new DateTime(CurrentMonth.Year, CurrentMonth.Month, 1);
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
        var startDate = firstDayOfMonth.AddDays(-(int)firstDayOfMonth.DayOfWeek);

        for (int i = 0; i < 42; i++) // 6 weeks * 7 days
        {
            var date = startDate.AddDays(i);
            var calendarDay = new CalendarDay
            {
                Date = date,
                IsCurrentMonth = date.Month == CurrentMonth.Month,
                IsToday = date.Date == DateTime.Today,
                IsSelected = SelectedDates.Contains(date.Date),
                IsAvailable = date >= DateTime.Today,
                DayNumber = date.Day
            };

            CalendarDays.Add(calendarDay);
        }
    }

    [RelayCommand]
    private async Task SelectDateAsync(CalendarDay day)
    {
        if (!day.IsAvailable || !day.IsCurrentMonth) return;

        if (CurrentUser?.UserType == UserType.PetOwner || CurrentUser?.UserType == UserType.Both)
        {
            await HandleOwnerDateSelectionAsync(day);
        }
        else if (CurrentUser?.UserType == UserType.PetSitter)
        {
            await HandleSitterDateSelectionAsync(day);
        }
    }

    private async Task HandleOwnerDateSelectionAsync(CalendarDay day)
    {
        if (!IsSelectingDateRange)
        {
            // Start date range selection
            StartDate = day.Date;
            EndDate = null;
            IsSelectingDateRange = true;
            SelectionMode = "End Date";
            
            // Clear previous selections
            foreach (var calDay in CalendarDays)
            {
                calDay.IsSelected = false;
                calDay.IsInRange = false;
            }
            
            day.IsSelected = true;
        }
        else
        {
            // Complete date range selection
            if (day.Date < StartDate)
            {
                // Swap dates if end is before start
                EndDate = StartDate;
                StartDate = day.Date;
            }
            else
            {
                EndDate = day.Date;
            }

            IsSelectingDateRange = false;
            SelectionMode = "Start Date";

            // Update visual selection
            UpdateDateRangeSelection();

            // Navigate to create booking page
            await NavigateToCreateBookingAsync();
        }
    }

    private async Task HandleSitterDateSelectionAsync(CalendarDay day)
    {
        SelectedDay = day.Date;
        
        // Load bookings for selected day
        if (CurrentUser != null)
        {
            var bookings = await _schedulingService.GetBookingsForDateAsync(CurrentUser.Id, day.Date);
            DayBookings = bookings.ToList();
            ShowBookingDetails = DayBookings.Any();
        }

        // Update selection visual
        foreach (var calDay in CalendarDays)
        {
            calDay.IsSelected = calDay.Date.Date == day.Date.Date;
        }
    }

    private void UpdateDateRangeSelection()
    {
        if (StartDate == null || EndDate == null) return;

        foreach (var day in CalendarDays)
        {
            day.IsSelected = day.Date.Date == StartDate.Value.Date || day.Date.Date == EndDate.Value.Date;
            day.IsInRange = day.Date.Date > StartDate.Value.Date && day.Date.Date < EndDate.Value.Date;
        }
    }

    [RelayCommand]
    private async Task NavigateToCreateBookingAsync()
    {
        if (StartDate == null || EndDate == null) return;

        var navigationParameter = new Dictionary<string, object>
        {
            { "StartDate", StartDate.Value },
            { "EndDate", EndDate.Value }
        };

        await Shell.Current.GoToAsync("createrequest", navigationParameter);
    }

    [RelayCommand]
    private async Task ViewBookingDetailsAsync(Booking booking)
    {
        if (booking == null) return;

        var navigationParameter = new Dictionary<string, object>
        {
            { "BookingId", booking.Id }
        };

        await Shell.Current.GoToAsync("bookingdetails", navigationParameter);
    }

    [RelayCommand]
    private void PreviousMonth()
    {
        CurrentMonth = CurrentMonth.AddMonths(-1);
        GenerateCalendarDays();
        Task.Run(async () => await LoadCalendarDataAsync());
    }

    [RelayCommand]
    private void NextMonth()
    {
        CurrentMonth = CurrentMonth.AddMonths(1);
        GenerateCalendarDays();
        Task.Run(async () => await LoadCalendarDataAsync());
    }

    [RelayCommand]
    private void ClearSelection()
    {
        StartDate = null;
        EndDate = null;
        IsSelectingDateRange = false;
        SelectionMode = "Start Date";
        ShowBookingDetails = false;

        foreach (var day in CalendarDays)
        {
            day.IsSelected = false;
            day.IsInRange = false;
        }
    }

    partial void OnCurrentMonthChanged(DateTime value)
    {
        GenerateCalendarDays();
        Task.Run(async () => await LoadCalendarDataAsync());
    }
}

public partial class CalendarDay : ObservableObject
{
    [ObservableProperty]
    private DateTime date;

    [ObservableProperty]
    private int dayNumber;

    [ObservableProperty]
    private bool isCurrentMonth;

    [ObservableProperty]
    private bool isToday;

    [ObservableProperty]
    private bool isSelected;

    [ObservableProperty]
    private bool isInRange;

    [ObservableProperty]
    private bool isAvailable;

    [ObservableProperty]
    private bool hasBookings;

    [ObservableProperty]
    private int bookingCount;

    [ObservableProperty]
    private BookingStatus bookingStatus;

    public Color BackgroundColor
    {
        get
        {
            if (IsSelected) return Color.FromArgb("#007AFF");
            if (IsInRange) return Color.FromArgb("#E3F2FD");
            if (IsToday) return Color.FromArgb("#FFF3E0");
            if (HasBookings) return Color.FromArgb("#E8F5E8");
            if (!IsCurrentMonth) return Color.FromArgb("#F5F5F5");
            return Colors.Transparent;
        }
    }

    public Color TextColor
    {
        get
        {
            if (IsSelected) return Colors.White;
            if (!IsCurrentMonth) return Color.FromArgb("#BDBDBD");
            if (!IsAvailable) return Color.FromArgb("#BDBDBD");
            return Color.FromArgb("#212121");
        }
    }
}
