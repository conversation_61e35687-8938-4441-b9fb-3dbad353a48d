<Paths><ManifestDocuments><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/1.2.0.9/buildTransitive/net8.0-android34.0/../../aar/androidx.versionedparcelable.versionedparcelable.aar">obj/Debug/net9.0-android/lp/100/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/1.2.0.9/buildTransitive/net8.0-android34.0/../../aar/androidx.tracing.tracing.aar">obj/Debug/net9.0-android/lp/101/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/1.2.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.startup.startup-runtime.aar">obj/Debug/net9.0-android/lp/102/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/1.4.1.2/buildTransitive/net8.0-android34.0/../../aar/androidx.profileinstaller.profileinstaller.aar">obj/Debug/net9.0-android/lp/103/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/2.2.0.15/buildTransitive/net8.0-android34.0/../../aar/androidx.arch.core.core-runtime.aar">obj/Debug/net9.0-android/lp/104/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-android.aar">obj/Debug/net9.0-android/lp/105/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime.aar">obj/Debug/net9.0-android/lp/106/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.interpolator.interpolator.aar">obj/Debug/net9.0-android/lp/107/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/1.4.1.8/buildTransitive/net8.0-android34.0/../../aar/androidx.annotation.annotation-experimental.aar">obj/Debug/net9.0-android/lp/108/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core/1.15.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core.aar">obj/Debug/net9.0-android/lp/109/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview/1.1.0.30/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview.aar">obj/Debug/net9.0-android/lp/110/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/1.1.0.1/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager.viewpager.aar">obj/Debug/net9.0-android/lp/111/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate.aar">obj/Debug/net9.0-android/lp/112/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/1.15.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core-ktx.aar">obj/Debug/net9.0-android/lp/113/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/1.0.0.17/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview-poolingcontainer.aar">obj/Debug/net9.0-android/lp/114/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/1.3.2.10/buildTransitive/net8.0-android34.0/../../aar/androidx.recyclerview.recyclerview.aar">obj/Debug/net9.0-android/lp/115/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-android.aar">obj/Debug/net9.0-android/lp/116/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel.aar">obj/Debug/net9.0-android/lp/117/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core.aar">obj/Debug/net9.0-android/lp/118/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.loader/1.1.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.loader.loader.aar">obj/Debug/net9.0-android/lp/119/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-savedstate.aar">obj/Debug/net9.0-android/lp/120/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity/1.9.3.2/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity.aar">obj/Debug/net9.0-android/lp/121/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/1.8.5.2/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment.aar">obj/Debug/net9.0-android/lp/122/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/1.1.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager2.viewpager2.aar">obj/Debug/net9.0-android/lp/123/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/1.2.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable.aar">obj/Debug/net9.0-android/lp/124/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/1.2.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable-animated.aar">obj/Debug/net9.0-android/lp/125/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.print/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.print.print.aar">obj/Debug/net9.0-android/lp/126/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/1.1.0.19/buildTransitive/net8.0-android34.0/../../aar/androidx.localbroadcastmanager.localbroadcastmanager.aar">obj/Debug/net9.0-android/lp/127/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/1.0.1.31/buildTransitive/net8.0-android34.0/../../aar/androidx.documentfile.documentfile.aar">obj/Debug/net9.0-android/lp/128/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.legacy.legacy-support-core-utils.aar">obj/Debug/net9.0-android/lp/129/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.dynamicanimation.dynamicanimation.aar">obj/Debug/net9.0-android/lp/130/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.transition/1.5.1.4/buildTransitive/net8.0-android34.0/../../aar/androidx.transition.transition.aar">obj/Debug/net9.0-android/lp/131/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-process.aar">obj/Debug/net9.0-android/lp/132/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/1.5.0.3/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2.aar">obj/Debug/net9.0-android/lp/133/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/1.5.0.3/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2-views-helper.aar">obj/Debug/net9.0-android/lp/134/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/1.2.0.15/buildTransitive/net8.0-android34.0/../../aar/androidx.drawerlayout.drawerlayout.aar">obj/Debug/net9.0-android/lp/135/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.cursoradapter.cursoradapter.aar">obj/Debug/net9.0-android/lp/136/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.coordinatorlayout.coordinatorlayout.aar">obj/Debug/net9.0-android/lp/137/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/1.7.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat-resources.aar">obj/Debug/net9.0-android/lp/138/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/1.7.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat.aar">obj/Debug/net9.0-android/lp/139/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/2.2.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.constraintlayout.constraintlayout.aar">obj/Debug/net9.0-android/lp/140/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/1.0.0.33/buildTransitive/net8.0-android34.0/../../aar/androidx.cardview.cardview.aar">obj/Debug/net9.0-android/lp/141/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.android.material/1.12.0.2/buildTransitive/net8.0-android34.0/../../aar/com.google.android.material.material.aar">obj/Debug/net9.0-android/lp/142/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/********/buildTransitive/net8.0-android34.0/../../aar/androidx.window.extensions.core.core.aar">obj/Debug/net9.0-android/lp/143/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.window.window.aar">obj/Debug/net9.0-android/lp/144/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/*******4/buildTransitive/net8.0-android34.0/../../aar/androidx.swiperefreshlayout.swiperefreshlayout.aar">obj/Debug/net9.0-android/lp/145/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.slidingpanelayout.slidingpanelayout.aar">obj/Debug/net9.0-android/lp/146/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/buildTransitive/net8.0-android34.0/../../aar/androidx.security.security-crypto.aar">obj/Debug/net9.0-android/lp/147/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate-ktx.aar">obj/Debug/net9.0-android/lp/148/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-ktx.aar">obj/Debug/net9.0-android/lp/149/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-ktx-android.aar">obj/Debug/net9.0-android/lp/150/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-ktx.aar">obj/Debug/net9.0-android/lp/151/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-common.aar">obj/Debug/net9.0-android/lp/152/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/1.9.3.2/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity-ktx.aar">obj/Debug/net9.0-android/lp/153/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-runtime.aar">obj/Debug/net9.0-android/lp/154/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-ui.aar">obj/Debug/net9.0-android/lp/155/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core-ktx.aar">obj/Debug/net9.0-android/lp/156/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/1.8.5.2/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment-ktx.aar">obj/Debug/net9.0-android/lp/157/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-fragment.aar">obj/Debug/net9.0-android/lp/158/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata.aar">obj/Debug/net9.0-android/lp/159/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/1.3.7.9/buildTransitive/net8.0-android34.0/../../aar/androidx.exifinterface.exifinterface.aar">obj/Debug/net9.0-android/lp/160/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.browser/1.8.0.8/buildTransitive/net8.0-android34.0/../../aar/androidx.browser.browser.aar">obj/Debug/net9.0-android/lp/161/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.70/lib/net9.0-android35.0/maui.aar" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.70">obj/Debug/net9.0-android/lp/165/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide/4.16.0.11/lib/net8.0-android34.0/glide.aar" NuGetPackageId="Xamarin.Android.Glide" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/171/jl/AndroidManifest.xml</ManifestDocument><ManifestDocument OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/4.16.0.11/lib/net8.0-android34.0/gifdecoder.aar" NuGetPackageId="Xamarin.Android.Glide.GifDecoder" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/172/jl/AndroidManifest.xml</ManifestDocument></ManifestDocuments><NativeLibraries><NativeLibrary OriginalFile="/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.android/2.1.10/lib/net6.0-android31.0/SQLitePCLRaw.lib.e_sqlite3.android.aar" NuGetPackageId="SQLitePCLRaw.lib.e_sqlite3.android" NuGetPackageVersion="2.1.10">obj/Debug/net9.0-android/lp/168/jl/jni/armeabi-v7a/libe_sqlite3.so</NativeLibrary><NativeLibrary OriginalFile="/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.android/2.1.10/lib/net6.0-android31.0/SQLitePCLRaw.lib.e_sqlite3.android.aar" NuGetPackageId="SQLitePCLRaw.lib.e_sqlite3.android" NuGetPackageVersion="2.1.10">obj/Debug/net9.0-android/lp/168/jl/jni/x86/libe_sqlite3.so</NativeLibrary><NativeLibrary OriginalFile="/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.android/2.1.10/lib/net6.0-android31.0/SQLitePCLRaw.lib.e_sqlite3.android.aar" NuGetPackageId="SQLitePCLRaw.lib.e_sqlite3.android" NuGetPackageVersion="2.1.10">obj/Debug/net9.0-android/lp/168/jl/jni/arm64-v8a/libe_sqlite3.so</NativeLibrary><NativeLibrary OriginalFile="/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.android/2.1.10/lib/net6.0-android31.0/SQLitePCLRaw.lib.e_sqlite3.android.aar" NuGetPackageId="SQLitePCLRaw.lib.e_sqlite3.android" NuGetPackageVersion="2.1.10">obj/Debug/net9.0-android/lp/168/jl/jni/x86_64/libe_sqlite3.so</NativeLibrary></NativeLibraries><Jars><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/1.2.0.9/buildTransitive/net8.0-android34.0/../../aar/androidx.versionedparcelable.versionedparcelable.aar">obj/Debug/net9.0-android/lp/100/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/1.2.0.9/buildTransitive/net8.0-android34.0/../../aar/androidx.tracing.tracing.aar">obj/Debug/net9.0-android/lp/101/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/1.2.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.startup.startup-runtime.aar">obj/Debug/net9.0-android/lp/102/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/1.4.1.2/buildTransitive/net8.0-android34.0/../../aar/androidx.profileinstaller.profileinstaller.aar">obj/Debug/net9.0-android/lp/103/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/2.2.0.15/buildTransitive/net8.0-android34.0/../../aar/androidx.arch.core.core-runtime.aar">obj/Debug/net9.0-android/lp/104/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-android.aar">obj/Debug/net9.0-android/lp/105/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.interpolator.interpolator.aar">obj/Debug/net9.0-android/lp/107/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/1.4.1.8/buildTransitive/net8.0-android34.0/../../aar/androidx.annotation.annotation-experimental.aar">obj/Debug/net9.0-android/lp/108/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core/1.15.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core.aar">obj/Debug/net9.0-android/lp/109/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview/1.1.0.30/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview.aar">obj/Debug/net9.0-android/lp/110/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/1.1.0.1/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager.viewpager.aar">obj/Debug/net9.0-android/lp/111/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate.aar">obj/Debug/net9.0-android/lp/112/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/1.15.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core-ktx.aar">obj/Debug/net9.0-android/lp/113/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/1.0.0.17/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview-poolingcontainer.aar">obj/Debug/net9.0-android/lp/114/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/1.3.2.10/buildTransitive/net8.0-android34.0/../../aar/androidx.recyclerview.recyclerview.aar">obj/Debug/net9.0-android/lp/115/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-android.aar">obj/Debug/net9.0-android/lp/116/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core.aar">obj/Debug/net9.0-android/lp/118/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.loader/1.1.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.loader.loader.aar">obj/Debug/net9.0-android/lp/119/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-savedstate.aar">obj/Debug/net9.0-android/lp/120/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity/1.9.3.2/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity.aar">obj/Debug/net9.0-android/lp/121/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/1.8.5.2/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment.aar">obj/Debug/net9.0-android/lp/122/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/1.1.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager2.viewpager2.aar">obj/Debug/net9.0-android/lp/123/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/1.2.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable.aar">obj/Debug/net9.0-android/lp/124/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/1.2.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable-animated.aar">obj/Debug/net9.0-android/lp/125/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.print/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.print.print.aar">obj/Debug/net9.0-android/lp/126/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/1.1.0.19/buildTransitive/net8.0-android34.0/../../aar/androidx.localbroadcastmanager.localbroadcastmanager.aar">obj/Debug/net9.0-android/lp/127/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/1.0.1.31/buildTransitive/net8.0-android34.0/../../aar/androidx.documentfile.documentfile.aar">obj/Debug/net9.0-android/lp/128/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.legacy.legacy-support-core-utils.aar">obj/Debug/net9.0-android/lp/129/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.dynamicanimation.dynamicanimation.aar">obj/Debug/net9.0-android/lp/130/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.transition/1.5.1.4/buildTransitive/net8.0-android34.0/../../aar/androidx.transition.transition.aar">obj/Debug/net9.0-android/lp/131/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-process.aar">obj/Debug/net9.0-android/lp/132/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/1.5.0.3/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2.aar">obj/Debug/net9.0-android/lp/133/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/1.5.0.3/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2.aar">obj/Debug/net9.0-android/lp/133/jl/libs/repackaged.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/1.5.0.3/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2-views-helper.aar">obj/Debug/net9.0-android/lp/134/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/1.2.0.15/buildTransitive/net8.0-android34.0/../../aar/androidx.drawerlayout.drawerlayout.aar">obj/Debug/net9.0-android/lp/135/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/1.0.0.31/buildTransitive/net8.0-android34.0/../../aar/androidx.cursoradapter.cursoradapter.aar">obj/Debug/net9.0-android/lp/136/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.coordinatorlayout.coordinatorlayout.aar">obj/Debug/net9.0-android/lp/137/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/1.7.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat-resources.aar">obj/Debug/net9.0-android/lp/138/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/1.7.0.5/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat.aar">obj/Debug/net9.0-android/lp/139/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/2.2.0.2/buildTransitive/net8.0-android34.0/../../aar/androidx.constraintlayout.constraintlayout.aar">obj/Debug/net9.0-android/lp/140/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/1.0.0.33/buildTransitive/net8.0-android34.0/../../aar/androidx.cardview.cardview.aar">obj/Debug/net9.0-android/lp/141/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.android.material/1.12.0.2/buildTransitive/net8.0-android34.0/../../aar/com.google.android.material.material.aar">obj/Debug/net9.0-android/lp/142/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/********/buildTransitive/net8.0-android34.0/../../aar/androidx.window.extensions.core.core.aar">obj/Debug/net9.0-android/lp/143/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.window.window.aar">obj/Debug/net9.0-android/lp/144/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/*******4/buildTransitive/net8.0-android34.0/../../aar/androidx.swiperefreshlayout.swiperefreshlayout.aar">obj/Debug/net9.0-android/lp/145/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.slidingpanelayout.slidingpanelayout.aar">obj/Debug/net9.0-android/lp/146/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/buildTransitive/net8.0-android34.0/../../aar/androidx.security.security-crypto.aar">obj/Debug/net9.0-android/lp/147/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate-ktx.aar">obj/Debug/net9.0-android/lp/148/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-ktx.aar">obj/Debug/net9.0-android/lp/149/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-ktx-android.aar">obj/Debug/net9.0-android/lp/150/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-common.aar">obj/Debug/net9.0-android/lp/152/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/1.9.3.2/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity-ktx.aar">obj/Debug/net9.0-android/lp/153/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-runtime.aar">obj/Debug/net9.0-android/lp/154/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-ui.aar">obj/Debug/net9.0-android/lp/155/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core-ktx.aar">obj/Debug/net9.0-android/lp/156/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/1.8.5.2/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment-ktx.aar">obj/Debug/net9.0-android/lp/157/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/2.8.5.1/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-fragment.aar">obj/Debug/net9.0-android/lp/158/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata.aar">obj/Debug/net9.0-android/lp/159/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/1.3.7.9/buildTransitive/net8.0-android34.0/../../aar/androidx.exifinterface.exifinterface.aar">obj/Debug/net9.0-android/lp/160/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.browser/1.8.0.8/buildTransitive/net8.0-android34.0/../../aar/androidx.browser.browser.aar">obj/Debug/net9.0-android/lp/161/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/googlegson/2.11.0.5/lib/net8.0-android34.0/GoogleGson.aar" NuGetPackageId="GoogleGson" NuGetPackageVersion="2.11.0.5">obj/Debug/net9.0-android/lp/162/jl/libs/ED64959F88B22E6D.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.code.findbugs.jsr305/3.0.2.18/lib/net8.0-android34.0/Jsr305Binding.aar" NuGetPackageId="Xamarin.Google.Code.FindBugs.JSR305" NuGetPackageVersion="3.0.2.18">obj/Debug/net9.0-android/lp/163/jl/libs/2E7FD15AFA9B216B.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.70/lib/net9.0-android35.0/maui.aar" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.70">obj/Debug/net9.0-android/lp/165/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.annotations/4.16.0.11/lib/net8.0-android34.0/Xamarin.Android.Glide.Annotations.aar" NuGetPackageId="Xamarin.Android.Glide.Annotations" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/169/jl/libs/659D48BEBA477FDD.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.disklrucache/4.16.0.11/lib/net8.0-android34.0/Xamarin.Android.Glide.DiskLruCache.aar" NuGetPackageId="Xamarin.Android.Glide.DiskLruCache" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/170/jl/libs/95D547F40BE4687C.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide/4.16.0.11/lib/net8.0-android34.0/glide.aar" NuGetPackageId="Xamarin.Android.Glide" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/171/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/4.16.0.11/lib/net8.0-android34.0/gifdecoder.aar" NuGetPackageId="Xamarin.Android.Glide.GifDecoder" NuGetPackageVersion="4.16.0.11">obj/Debug/net9.0-android/lp/172/jl/classes.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.crypto.tink.android/1.16.0.1/lib/net8.0-android34.0/Xamarin.Google.Crypto.Tink.Android.aar" NuGetPackageId="Xamarin.Google.Crypto.Tink.Android" NuGetPackageVersion="1.16.0.1">obj/Debug/net9.0-android/lp/173/jl/libs/F975D0960055A5E3.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.errorprone.annotations/2.36.0.1/lib/net8.0-android34.0/Xamarin.Google.ErrorProne.Annotations.aar" NuGetPackageId="Xamarin.Google.ErrorProne.Annotations" NuGetPackageVersion="2.36.0.1">obj/Debug/net9.0-android/lp/174/jl/libs/B71CFF5D5A0B3AEB.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.guava.listenablefuture/1.0.0.26/lib/net8.0-android34.0/Xamarin.Google.Guava.ListenableFuture.aar" NuGetPackageId="Xamarin.Google.Guava.ListenableFuture" NuGetPackageVersion="1.0.0.26">obj/Debug/net9.0-android/lp/175/jl/libs/8956669C0037225B.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.jetbrains.annotations/26.0.1.2/lib/net8.0-android34.0/Xamarin.Jetbrains.Annotations.aar" NuGetPackageId="Xamarin.Jetbrains.Annotations" NuGetPackageVersion="26.0.1.2">obj/Debug/net9.0-android/lp/176/jl/libs/05D34BA5CA8637CF.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib/2.0.21.2/lib/net8.0-android34.0/Xamarin.Kotlin.StdLib.aar" NuGetPackageId="Xamarin.Kotlin.StdLib" NuGetPackageVersion="2.0.21.2">obj/Debug/net9.0-android/lp/177/jl/libs/F91D79B39A269A7E.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.aar" NuGetPackageId="Xamarin.KotlinX.AtomicFU" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/178/jl/libs/327D95CCFE836D7B.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu.jvm/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.AtomicFU.Jvm" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/179/jl/libs/C7DE71AD77BC3A1F.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.android/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Android.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Android" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/180/jl/libs/F01C7EADE990A004.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/181/jl/libs/F6DB49E0CC905A9A.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core.jvm/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core.Jvm" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/182/jl/libs/A1A754E9E8693F85.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.aar" NuGetPackageId="Xamarin.KotlinX.Serialization.Core" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/183/jl/libs/B7446A23346A729D.jar</Jar><Jar OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core.jvm/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.Serialization.Core.Jvm" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/184/jl/libs/623E6E29E2058512.jar</Jar></Jars></Paths>