aotassemblies=false
androidaddkeepalives=
androidaotmode=interpreter
androidembedprofilers=
androidenableprofiledaot=
androiddextool=d8
androidlinktool=
androidlinkresources=
androidpackageformat=apk
embedassembliesintoapk=false
androidlinkmode=none
androidlinkskip=
androidsdkbuildtoolsversion=35.0.0
androidsdkpath=/users/maheshgadhave/library/android/sdk/
androidndkpath=
javasdkpath=/library/java/javavirtualmachines/openjdk-17.jdk/contents/home/
androidsequencepointsmode=none
androidnetsdkversion=35.0.78
monosymbolarchive=false
androiduselatestplatformsdk=false
targetframeworkversion=v9.0
androidcreatepackageperabi=
androidgeneratejnimarshalmethods=false
os=unix
androidincludedebugsymbols=true
androidpackagenamingpolicy=lowercasecrc64
_nugetassetsfilehash=fefa30a1a4236ad4106836541c04aeb27d39a962e9e75c2c16c1ade426876679
typemapkind=strings-asm
androidmanifestplaceholders=
projectfullpath=/users/maheshgadhave/documents/augment-projects/petsitter/petsitterconnect/petsitterconnect.csproj
androidusedesignerassembly=true
