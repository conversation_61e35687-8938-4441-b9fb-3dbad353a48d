<resources><!-- Base application theme. --><style name="Maui.MainTheme.Base" parent="Theme.MaterialComponents.DayNight"><!-- For .NET 9 we optout of edge to edge enforcement by default --><item name="maui_edgetoedge_optout">true</item><item name="maui_splash">false</item><item name="colorPrimary">@color/colorPrimary</item><item name="colorPrimaryDark">@color/colorPrimaryDark</item><item name="colorAccent">@color/colorAccent</item><item name="android:actionMenuTextColor">@color/colorActionMenuTextColor</item><item name="appBarLayoutStyle">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item><item name="bottomNavigationViewStyle">@style/Widget.Design.BottomNavigationView</item><item name="materialButtonStyle">@style/MauiMaterialButton</item><item name="checkboxStyle">@style/MauiCheckBox</item><item name="android:textAllCaps">false</item><item name="alertDialogTheme">@style/MauiAlertDialogTheme</item></style><style name="Maui.MainTheme" parent="Maui.MainTheme.Base"><!-- v35+ uses this to inject edge to edge opt out attribute value --></style><style name="Maui.MainTheme.NoActionBar" parent="Maui.MainTheme"><item name="windowActionBar">false</item><item name="windowNoTitle">true</item><item name="windowActionModeOverlay">true</item><item name="android:actionModeBackground">?attr/colorPrimary</item><item name="android:actionModeStyle">@style/ActionMode</item></style><style name="ActionMode"><item name="android:background">?attr/colorPrimary</item><item name="android:height">?attr/actionBarSize</item></style><!-- Splash theme --><style name="Maui.SplashTheme" parent="Maui.MainTheme.NoActionBar"><item name="maui_splash">true</item><item name="android:windowBackground">@drawable/maui_splash</item><!--
          Android 12+ specific settings
          See: https://developer.android.com/guide/topics/ui/splash-screen#set-theme
        --><item name="android:windowSplashScreenBackground">@color/maui_splash_color</item><item name="android:windowSplashScreenAnimatedIcon">@drawable/maui_splash</item></style><!-- Themes for Xamarin.Forms backwards compatibility --><style name="MainTheme" parent="Maui.MainTheme" /><style name="MainTheme.Base" parent="Maui.MainTheme" /><style name="MainTheme.NoActionBar" parent="Maui.MainTheme.NoActionBar" /><style name="AppTheme" parent="Maui.MainTheme" /><style name="AppTheme.NoActionBar" parent="Maui.MainTheme.NoActionBar" /><style name="MauiMaterialButton" parent="Widget.MaterialComponents.Button.UnelevatedButton"><!-- remove all the min sizes as MAUI manages it --><item name="android:minWidth">0dp</item><item name="android:minHeight">0dp</item><!-- padding is the space between the edge of the background and the contents  --><item name="android:paddingLeft">0dp</item><item name="android:paddingRight">0dp</item><item name="android:paddingTop">0dp</item><item name="android:paddingBottom">0dp</item><!-- insets are the amount of space btween the actual color and the edge of the view frame --><item name="android:insetTop">0dp</item><item name="android:insetBottom">0dp</item><item name="android:insetLeft">0dp</item><item name="android:insetRight">0dp</item></style><style name="MauiCheckBox" parent="Widget.Material3.CompoundButton.CheckBox"><!-- remove all the min sizes as MAUI manages it --><item name="android:minWidth">0dp</item><item name="android:minHeight">0dp</item><!-- remove the button tint as MAUI manages it --><item name="buttonTint">?attr/colorPrimary</item></style><style name="MauiAlertDialogTheme" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog"><item name="colorPrimary">?attr/colorOnSurface</item></style><!-- 
    The collectionViewScrollBars style will be used as the default style for ItemsViewRenderer (the base renderer
    for CollectionView and CarouselView. We have to use a style to set up the scrollbars because there is currently
    no way to add them via code.
    
    When the renderer is created, we wrap its Context's theme with collectionViewTheme; that way, the 
    collectionViewScrollBars style will be defined. With the style defined (and with the collectionViewStyle 
    attribute defined in attrs.xml), we can apply the collectionViewScrollBars style explicitly to the renderer we are
    creating (and avoid forcing every child control to have scrollbars).
  --><style name="scrollViewScrollBars"><item name="android:scrollbars">vertical|horizontal</item></style><style name="collectionViewTheme"><item name="android:scrollbars">vertical|horizontal</item></style><style name="scrollViewTheme"><item name="scrollViewStyle">@style/scrollViewScrollBars</item></style></resources>