# Baseline Profiles for navigation-common

HSPLandroidx/navigation/NavAction;-><init>(ILandroidx/navigation/NavOptions;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavAction;-><init>(ILandroidx/navigation/NavOptions;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavAction;->getDefaultArguments()Landroid/os/Bundle;
HSPLandroidx/navigation/NavAction;->getDestinationId()I
HSPLandroidx/navigation/NavAction;->getNavOptions()Landroidx/navigation/NavOptions;
HSPLandroidx/navigation/NavAction;->setNavOptions(Landroidx/navigation/NavOptions;)V
HSPLandroidx/navigation/NavArgument$Builder;-><init>()V
HSPLandroidx/navigation/NavArgument$Builder;->build()Landroidx/navigation/NavArgument;
HSPLandroidx/navigation/NavArgument$Builder;->setIsNullable(Z)Landroidx/navigation/NavArgument$Builder;
HSPLandroidx/navigation/NavArgument$Builder;->setType(Landroidx/navigation/NavType;)Landroidx/navigation/NavArgument$Builder;
HSPLandroidx/navigation/NavArgument;-><init>(Landroidx/navigation/NavType;ZLjava/lang/Object;Z)V
HSPLandroidx/navigation/NavArgument;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavArgument;->hashCode()I
HSPLandroidx/navigation/NavBackStackEntry$Companion;-><init>()V
HSPLandroidx/navigation/NavBackStackEntry$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavBackStackEntry$Companion;->create$default(Landroidx/navigation/NavBackStackEntry$Companion;Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;ILjava/lang/Object;)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavBackStackEntry$Companion;->create(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavBackStackEntry$defaultFactory$2;-><init>(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavBackStackEntry$savedStateHandle$2;-><init>(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavBackStackEntry;-><clinit>()V
HSPLandroidx/navigation/NavBackStackEntry;-><init>(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavBackStackEntry;-><init>(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavBackStackEntry;->getArguments()Landroid/os/Bundle;
HSPLandroidx/navigation/NavBackStackEntry;->getDestination()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavBackStackEntry;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/navigation/NavBackStackEntry;->getMaxLifecycle()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/navigation/NavBackStackEntry;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/navigation/NavBackStackEntry;->handleLifecycleEvent(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/navigation/NavBackStackEntry;->hashCode()I
HSPLandroidx/navigation/NavBackStackEntry;->setMaxLifecycle(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/navigation/NavBackStackEntry;->updateState()V
HSPLandroidx/navigation/NavDeepLinkRequest;-><init>(Landroid/content/Intent;)V
HSPLandroidx/navigation/NavDeepLinkRequest;-><init>(Landroid/net/Uri;Ljava/lang/String;Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination$Companion;-><init>()V
HSPLandroidx/navigation/NavDestination$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavDestination$Companion;->getDisplayName(Landroid/content/Context;I)Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;-><clinit>()V
HSPLandroidx/navigation/NavDestination;-><init>(Landroidx/navigation/Navigator;)V
HSPLandroidx/navigation/NavDestination;-><init>(Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination;->addArgument(Ljava/lang/String;Landroidx/navigation/NavArgument;)V
HSPLandroidx/navigation/NavDestination;->addInDefaultArgs(Landroid/os/Bundle;)Landroid/os/Bundle;
HSPLandroidx/navigation/NavDestination;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavDestination;->getArguments()Ljava/util/Map;
HSPLandroidx/navigation/NavDestination;->getId()I
HSPLandroidx/navigation/NavDestination;->getNavigatorName()Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;->getParent()Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavDestination;->getRoute()Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;->hashCode()I
HSPLandroidx/navigation/NavDestination;->matchDeepLink(Landroidx/navigation/NavDeepLinkRequest;)Landroidx/navigation/NavDestination$DeepLinkMatch;
HSPLandroidx/navigation/NavDestination;->onInflate(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/navigation/NavDestination;->putAction(ILandroidx/navigation/NavAction;)V
HSPLandroidx/navigation/NavDestination;->setId(I)V
HSPLandroidx/navigation/NavDestination;->setLabel(Ljava/lang/CharSequence;)V
HSPLandroidx/navigation/NavDestination;->setParent(Landroidx/navigation/NavGraph;)V
HSPLandroidx/navigation/NavDestination;->setRoute(Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination;->supportsActions()Z
HSPLandroidx/navigation/NavGraph$Companion;-><init>()V
HSPLandroidx/navigation/NavGraph$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavGraph$iterator$1;-><init>(Landroidx/navigation/NavGraph;)V
HSPLandroidx/navigation/NavGraph$iterator$1;->hasNext()Z
HSPLandroidx/navigation/NavGraph$iterator$1;->next()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraph$iterator$1;->next()Ljava/lang/Object;
HSPLandroidx/navigation/NavGraph;-><clinit>()V
HSPLandroidx/navigation/NavGraph;-><init>(Landroidx/navigation/Navigator;)V
HSPLandroidx/navigation/NavGraph;->addDestination(Landroidx/navigation/NavDestination;)V
HSPLandroidx/navigation/NavGraph;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavGraph;->findNode(IZ)Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraph;->getNodes()Landroidx/collection/SparseArrayCompat;
HSPLandroidx/navigation/NavGraph;->getStartDestinationId()I
HSPLandroidx/navigation/NavGraph;->getStartDestinationRoute()Ljava/lang/String;
HSPLandroidx/navigation/NavGraph;->hashCode()I
HSPLandroidx/navigation/NavGraph;->iterator()Ljava/util/Iterator;
HSPLandroidx/navigation/NavGraph;->matchDeepLink(Landroidx/navigation/NavDeepLinkRequest;)Landroidx/navigation/NavDestination$DeepLinkMatch;
HSPLandroidx/navigation/NavGraph;->onInflate(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/navigation/NavGraph;->setStartDestinationId(I)V
HSPLandroidx/navigation/NavGraphNavigator;-><init>(Landroidx/navigation/NavigatorProvider;)V
HSPLandroidx/navigation/NavGraphNavigator;->createDestination()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraphNavigator;->createDestination()Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavGraphNavigator;->navigate(Landroidx/navigation/NavBackStackEntry;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;)V
HSPLandroidx/navigation/NavGraphNavigator;->navigate(Ljava/util/List;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;)V
HSPLandroidx/navigation/NavOptions$Builder;-><init>()V
HSPLandroidx/navigation/NavOptions$Builder;->build()Landroidx/navigation/NavOptions;
HSPLandroidx/navigation/NavOptions$Builder;->setEnterAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setExitAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setLaunchSingleTop(Z)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopEnterAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopExitAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopUpTo(IZZ)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setRestoreState(Z)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions;-><init>(ZZIZZIIII)V
HSPLandroidx/navigation/NavOptions;->hashCode()I
HSPLandroidx/navigation/NavOptions;->isPopUpToInclusive()Z
HSPLandroidx/navigation/NavOptions;->shouldLaunchSingleTop()Z
HSPLandroidx/navigation/NavOptions;->shouldPopUpToSaveState()Z
HSPLandroidx/navigation/NavOptions;->shouldRestoreState()Z
HSPLandroidx/navigation/NavType$Companion$BoolArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$BoolArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$BoolType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$BoolType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$FloatArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$FloatType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$IntType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$LongArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$LongArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$LongType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$LongType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$ReferenceType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion;-><init>()V
HSPLandroidx/navigation/NavType$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavType$Companion;->fromArgType(Ljava/lang/String;Ljava/lang/String;)Landroidx/navigation/NavType;
HSPLandroidx/navigation/NavType;-><clinit>()V
HSPLandroidx/navigation/NavType;-><init>(Z)V
HSPLandroidx/navigation/NavType;->isNullableAllowed()Z
HSPLandroidx/navigation/Navigator;-><init>()V
HSPLandroidx/navigation/Navigator;->getState()Landroidx/navigation/NavigatorState;
HSPLandroidx/navigation/Navigator;->isAttached()Z
HSPLandroidx/navigation/Navigator;->onAttach(Landroidx/navigation/NavigatorState;)V
HSPLandroidx/navigation/NavigatorProvider$Companion;-><init>()V
HSPLandroidx/navigation/NavigatorProvider$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavigatorProvider$Companion;->getNameForNavigator$navigation_common_release(Ljava/lang/Class;)Ljava/lang/String;
HSPLandroidx/navigation/NavigatorProvider$Companion;->validateName$navigation_common_release(Ljava/lang/String;)Z
HSPLandroidx/navigation/NavigatorProvider;-><clinit>()V
HSPLandroidx/navigation/NavigatorProvider;-><init>()V
HSPLandroidx/navigation/NavigatorProvider;->access$getAnnotationNames$cp()Ljava/util/Map;
HSPLandroidx/navigation/NavigatorProvider;->addNavigator(Landroidx/navigation/Navigator;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->addNavigator(Ljava/lang/String;Landroidx/navigation/Navigator;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->getNavigator(Ljava/lang/String;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->getNavigators()Ljava/util/Map;
HSPLandroidx/navigation/NavigatorState;-><init>()V
HSPLandroidx/navigation/NavigatorState;->getBackStack()Lkotlinx/coroutines/flow/StateFlow;
HSPLandroidx/navigation/NavigatorState;->getTransitionsInProgress()Lkotlinx/coroutines/flow/StateFlow;
HSPLandroidx/navigation/NavigatorState;->push(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavigatorState;->setNavigating(Z)V
Landroidx/navigation/FloatingWindow;
Landroidx/navigation/NavAction;
Landroidx/navigation/NavArgument$Builder;
Landroidx/navigation/NavArgument;
Landroidx/navigation/NavBackStackEntry$Companion;
Landroidx/navigation/NavBackStackEntry$defaultFactory$2;
Landroidx/navigation/NavBackStackEntry$savedStateHandle$2;
Landroidx/navigation/NavBackStackEntry;
Landroidx/navigation/NavDeepLinkRequest;
Landroidx/navigation/NavDestination$Companion;
Landroidx/navigation/NavDestination$DeepLinkMatch;
Landroidx/navigation/NavDestination;
Landroidx/navigation/NavGraph$Companion;
Landroidx/navigation/NavGraph$iterator$1;
Landroidx/navigation/NavGraph;
Landroidx/navigation/NavGraphNavigator;
Landroidx/navigation/NavOptions$Builder;
Landroidx/navigation/NavOptions;
Landroidx/navigation/NavType$Companion$BoolArrayType$1;
Landroidx/navigation/NavType$Companion$BoolType$1;
Landroidx/navigation/NavType$Companion$FloatArrayType$1;
Landroidx/navigation/NavType$Companion$FloatType$1;
Landroidx/navigation/NavType$Companion$IntArrayType$1;
Landroidx/navigation/NavType$Companion$IntType$1;
Landroidx/navigation/NavType$Companion$LongArrayType$1;
Landroidx/navigation/NavType$Companion$LongType$1;
Landroidx/navigation/NavType$Companion$ReferenceType$1;
Landroidx/navigation/NavType$Companion$StringArrayType$1;
Landroidx/navigation/NavType$Companion$StringType$1;
Landroidx/navigation/NavType$Companion;
Landroidx/navigation/NavType;
Landroidx/navigation/NavViewModelStoreProvider;
Landroidx/navigation/Navigator$Extras;
Landroidx/navigation/Navigator$Name;
Landroidx/navigation/Navigator;
Landroidx/navigation/NavigatorProvider$Companion;
Landroidx/navigation/NavigatorProvider;
Landroidx/navigation/NavigatorState;
HSPLandroidx/navigation/common/R$styleable;-><clinit>()V
Landroidx/navigation/common/R$styleable;
