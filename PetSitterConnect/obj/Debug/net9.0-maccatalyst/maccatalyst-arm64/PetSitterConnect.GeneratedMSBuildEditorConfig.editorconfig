is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net9.0-maccatalyst
build_property.TargetFramework = net9.0-maccatalyst
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.RootNamespace = PetSitterConnect
build_property.ProjectDir = /Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Colors.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Styles.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/BookingDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.BookingDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/BookingDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/BookingDetailPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/BookingListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.BookingListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/BookingListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/BookingListPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/CreatePetCareRequestPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.CreatePetCareRequestPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/CreatePetCareRequestPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/CreatePetCareRequestPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/LoginPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.LoginPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/LoginPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/LoginPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/PetCareRequestListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.PetCareRequestListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/PetCareRequestListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/PetCareRequestListPage.xaml

[/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/Views/RegisterPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.RegisterPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/RegisterPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/RegisterPage.xaml
