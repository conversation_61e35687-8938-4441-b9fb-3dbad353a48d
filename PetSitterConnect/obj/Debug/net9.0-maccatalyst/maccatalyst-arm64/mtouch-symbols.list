_SystemNative_Log
_SystemNative_Stat
_SystemNative_ConvertErrorPlatformToPal
_SystemNative_ConvertErrorPalToPlatform
_SystemNative_StrErrorR
_SystemNative_GetSid
_SystemNative_RegisterForSigChld
_SystemNative_GetEUid
_SystemNative_GetEGid
_SystemNative_GetHostName
_SystemNative_ForkAndExecProcess
_SystemNative_GetGroupList
_SystemNative_GetPwNamR
_SystemNative_GetPriority
_SystemNative_SetPriority
_SystemNative_InitializeTerminalAndSignalHandling
_SystemNative_Kill
_SystemNative_GetRLimit
_SystemNative_SetRLimit
_SystemNative_WaitIdAnyExitedNoHangNoWait
_SystemNative_WaitPidExitedNoHang
_SystemNative_Access
_SystemNative_GetGroups
_SystemNative_MkNod
_SystemNative_GetDeviceIdentifiers
_SystemNative_GetPwUidR
_SystemNative_GetGroupName
_SystemNative_Link
_SystemNative_MkFifo
_SystemNative_LStat
_BrotliDecoderDecompress
_BrotliDecoderDestroyInstance
_BrotliEncoderDestroyInstance
_BrotliEncoderCompress
_BrotliDecoderCreateInstance
_BrotliDecoderDecompressStream
_BrotliDecoderIsFinished
_BrotliEncoderCreateInstance
_BrotliEncoderSetParameter
_BrotliEncoderCompressStream
_BrotliEncoderHasMoreOutput
_SystemNative_FStat
_CompressionNative_DeflateInit2_
_CompressionNative_Deflate
_CompressionNative_DeflateEnd
_CompressionNative_InflateInit2_
_CompressionNative_Inflate
_CompressionNative_InflateEnd
_CompressionNative_Crc32
_SystemNative_GetAllMountPoints
_SystemNative_GetSpaceInfoForMountPoint
_SystemNative_GetFormatInfoForMountPoint
_SystemNative_Sync
_SystemNative_RealPath
_SystemNative_FcntlSetFD
_SystemNative_MMap
_SystemNative_MUnmap
_SystemNative_MSync
_SystemNative_SysConf
_SystemNative_FTruncate
_SystemNative_MAdvise
_SystemNative_ShmOpen
_SystemNative_ShmUnlink
_SystemNative_Unlink
_SystemNative_FcntlCanGetSetPipeSz
_SystemNative_FcntlGetPipeSz
_SystemNative_FcntlSetPipeSz
_SystemNative_SetEUid
_SystemNative_Close
_SystemNative_Pipe
_SystemNative_GetPeerID
_SystemNative_GetNameInfo
_SystemNative_FreeHostEntry
_SystemNative_Socket
_SystemNative_GetHostEntryForName
_SystemNative_EnumerateInterfaceAddresses
_SystemNative_EnumerateGatewayAddressesForInterface
_SystemNative_GetDomainName
_SystemNative_GetTcpGlobalStatistics
_SystemNative_GetIPv4GlobalStatistics
_SystemNative_GetUdpGlobalStatistics
_SystemNative_GetIcmpv4GlobalStatistics
_SystemNative_GetIcmpv6GlobalStatistics
_SystemNative_GetNumRoutes
_SystemNative_GetEstimatedTcpConnectionCount
_SystemNative_GetActiveTcpConnectionInfos
_SystemNative_GetEstimatedUdpListenerCount
_SystemNative_GetActiveUdpListeners
_SystemNative_GetNativeIPInterfaceStatistics
_SystemNative_GetSocketAddressSizes
_SystemNative_GetAddressFamily
_SystemNative_GetIPv4Address
_SystemNative_GetIPv6Address
_SystemNative_ReceiveSocketError
_SystemNative_SetAddressFamily
_SystemNative_GetPort
_SystemNative_SetPort
_SystemNative_SetIPv4Address
_SystemNative_SetIPv6Address
_SystemNative_InterfaceNameToIndex
_NetSecurityNative_ReleaseGssBuffer
_NetSecurityNative_EnsureGssInitialized
_NetSecurityNative_DisplayMinorStatus
_NetSecurityNative_DisplayMajorStatus
_NetSecurityNative_ImportUserName
_NetSecurityNative_ImportPrincipalName
_NetSecurityNative_ReleaseName
_NetSecurityNative_AcquireAcceptorCred
_NetSecurityNative_InitiateCredSpNego
_NetSecurityNative_InitiateCredWithPassword
_NetSecurityNative_ReleaseCred
_NetSecurityNative_InitSecContext
_NetSecurityNative_InitSecContextEx
_NetSecurityNative_AcceptSecContext
_NetSecurityNative_DeleteSecContext
_NetSecurityNative_GetUser
_NetSecurityNative_Wrap
_NetSecurityNative_Unwrap
_NetSecurityNative_GetMic
_NetSecurityNative_VerifyMic
_NetSecurityNative_IsNtlmInstalled
_AppleCryptoNative_SecCopyErrorMessageString
_AppleCryptoNative_SslCreateContext
_AppleCryptoNative_SslSetConnection
_AppleCryptoNative_SslSetMinProtocolVersion
_AppleCryptoNative_SslSetMaxProtocolVersion
_AppleCryptoNative_SslCopyCertChain
_AppleCryptoNative_SslCopyCADistinguishedNames
_AppleCryptoNative_SslSetBreakOnServerAuth
_AppleCryptoNative_SslSetBreakOnClientAuth
_AppleCryptoNative_SslSetBreakOnClientHello
_AppleCryptoNative_SslSetBreakOnCertRequested
_AppleCryptoNative_SslSetCertificate
_AppleCryptoNative_SslSetTargetName
_AppleCryptoNative_SSLSetALPNProtocols
_AppleCryptoNative_SSLSetALPNProtocol
_AppleCryptoNative_SslGetAlpnSelected
_AppleCryptoNative_SslHandshake
_AppleCryptoNative_SslSetAcceptClientCert
_AppleCryptoNative_SslSetIoCallbacks
_AppleCryptoNative_SslWrite
_AppleCryptoNative_SslRead
_AppleCryptoNative_SslIsHostnameMatch
_AppleCryptoNative_SslShutdown
_AppleCryptoNative_SslGetCipherSuite
_AppleCryptoNative_SslGetProtocolVersion
_AppleCryptoNative_SslSetEnabledCipherSuites
_AppleCryptoNative_SslSetCertificateAuthorities
_AppleCryptoNative_X509ChainGetChainSize
_AppleCryptoNative_X509ChainGetCertificateAtIndex
_SystemNative_FcntlSetIsNonBlocking
_SystemNative_FcntlGetIsNonBlocking
_SystemNative_FcntlGetFD
_SystemNative_Disconnect
_SystemNative_GetDomainSocketSizes
_SystemNative_GetMaximumAddressSize
_SystemNative_GetSockOpt
_SystemNative_GetControlMessageBufferSize
_SystemNative_SetLingerOption
_SystemNative_Poll
_SystemNative_PlatformSupportsDualModeIPv4PacketInfo
_SystemNative_SetSockOpt
_SystemNative_Shutdown
_SystemNative_CreateSocketEventPort
_SystemNative_CloseSocketEventPort
_SystemNative_CreateSocketEventBuffer
_SystemNative_FreeSocketEventBuffer
_SystemNative_TryChangeSocketEventRegistration
_SystemNative_WaitForSocketEvents
_SystemNative_Accept
_SystemNative_Bind
_SystemNative_Connect
_SystemNative_Connectx
_SystemNative_GetBytesAvailable
_SystemNative_GetAtOutOfBandMark
_SystemNative_GetPeerName
_SystemNative_GetSocketErrorOption
_SystemNative_GetSocketType
_SystemNative_GetSockName
_SystemNative_GetRawSockOpt
_SystemNative_TryGetIPPacketInformation
_SystemNative_GetLingerOption
_SystemNative_SendFile
_SystemNative_SetSendTimeout
_SystemNative_SetReceiveTimeout
_SystemNative_Listen
_SystemNative_GetIPv4MulticastOption
_SystemNative_SetIPv4MulticastOption
_SystemNative_GetIPv6MulticastOption
_SystemNative_SetIPv6MulticastOption
_SystemNative_Read
_SystemNative_Receive
_SystemNative_ReceiveMessage
_SystemNative_Send
_SystemNative_Select
_SystemNative_SendMessage
_SystemNative_SetRawSockOpt
_SystemNative_Write
_AppleCryptoNative_DigestFree
_AppleCryptoNative_DigestOneShot
_AppleCryptoNative_HmacFree
_AppleCryptoNative_HmacOneShot
_AppleCryptoNative_Pbkdf2
_AppleCryptoNative_GetRandomBytes
_AppleCryptoNative_CryptorFree
_AppleCryptoNative_GetOSStatusForChainStatus
_AppleCryptoNative_ChaCha20Poly1305Encrypt
_AppleCryptoNative_ChaCha20Poly1305Decrypt
_AppleCryptoNative_AesGcmEncrypt
_AppleCryptoNative_AesGcmDecrypt
_AppleCryptoNative_IsAuthenticationFailure
_AppleCryptoNative_DigestCreate
_AppleCryptoNative_DigestUpdate
_AppleCryptoNative_DigestFinal
_AppleCryptoNative_DigestCurrent
_AppleCryptoNative_DigestReset
_AppleCryptoNative_DigestClone
_AppleCryptoNative_EccGenerateKey
_AppleCryptoNative_EccGetKeySizeInBits
_AppleCryptoNative_HmacCreate
_AppleCryptoNative_HmacInit
_AppleCryptoNative_HmacUpdate
_AppleCryptoNative_HmacFinal
_AppleCryptoNative_HmacCurrent
_AppleCryptoNative_HmacClone
_AppleCryptoNative_EcdhKeyAgree
_AppleCryptoNative_RsaGenerateKey
_AppleCryptoNative_RsaSignaturePrimitive
_AppleCryptoNative_RsaVerificationPrimitive
_AppleCryptoNative_RsaEncryptionPrimitive
_AppleCryptoNative_RsaEncryptOaep
_AppleCryptoNative_RsaEncryptPkcs
_AppleCryptoNative_RsaDecryptOaep
_AppleCryptoNative_RsaDecryptRaw
_AppleCryptoNative_SecKeyGetSimpleKeySizeInBytes
_AppleCryptoNative_SecKeyCreateWithData
_AppleCryptoNative_SecKeyCopyExternalRepresentation
_AppleCryptoNative_SecKeyCopyPublicKey
_AppleCryptoNative_SecKeyVerifySignature
_AppleCryptoNative_SecKeyCreateSignature
_AppleCryptoNative_CryptorCreate
_AppleCryptoNative_CryptorUpdate
_AppleCryptoNative_CryptorReset
_AppleCryptoNative_X509GetRawData
_AppleCryptoNative_X509GetSubjectSummary
_AppleCryptoNative_X509GetPublicKey
_AppleCryptoNative_X509GetContentType
_AppleCryptoNative_X509CopyCertFromIdentity
_AppleCryptoNative_X509CopyPrivateKeyFromIdentity
_AppleCryptoNative_X509DemuxAndRetainHandle
_AppleCryptoNative_X509ChainCreateDefaultPolicy
_AppleCryptoNative_X509ChainCreateRevocationPolicy
_AppleCryptoNative_X509ChainCreate
_AppleCryptoNative_X509ChainEvaluate
_AppleCryptoNative_X509ChainGetTrustResults
_AppleCryptoNative_X509ChainGetStatusAtIndex
_AppleCryptoNative_X509ChainSetTrustAnchorCertificates
_AppleCryptoNative_SecKeychainEnumerateCerts
_AppleCryptoNative_SecKeychainEnumerateIdentities
_AppleCryptoNative_X509StoreAddCertificate
_AppleCryptoNative_X509StoreRemoveCertificate
_AppleCryptoNative_X509ImportCertificate
_AppleCryptoNative_X509ImportCollection
_GlobalizationNative_GetLatestJapaneseEra
_GlobalizationNative_GetLatestJapaneseEraNative
_GlobalizationNative_InitOrdinalCasingPage
_GlobalizationNative_CloseSortHandle
_GlobalizationNative_CompareString
_GlobalizationNative_IndexOf
_GlobalizationNative_LastIndexOf
_GlobalizationNative_GetSortKey
_GlobalizationNative_GetSortVersion
_GlobalizationNative_GetICUVersion
_GlobalizationNative_ToAscii
_GlobalizationNative_ToUnicode
_GlobalizationNative_IsNormalized
_GlobalizationNative_NormalizeString
_GlobalizationNative_IsNormalizedNative
_GlobalizationNative_NormalizeStringNative
_GlobalizationNative_GetCalendars
_GlobalizationNative_GetCalendarInfo
_GlobalizationNative_GetJapaneseEraStartDate
_GlobalizationNative_GetCalendarsNative
_GlobalizationNative_GetCalendarInfoNative
_GlobalizationNative_GetJapaneseEraStartDateNative
_GlobalizationNative_ChangeCase
_GlobalizationNative_ChangeCaseInvariant
_GlobalizationNative_ChangeCaseTurkish
_GlobalizationNative_ChangeCaseNative
_GlobalizationNative_ChangeCaseInvariantNative
_GlobalizationNative_GetSortHandle
_GlobalizationNative_StartsWith
_GlobalizationNative_EndsWith
_GlobalizationNative_CompareStringNative
_GlobalizationNative_EndsWithNative
_GlobalizationNative_IndexOfNative
_GlobalizationNative_StartsWithNative
_GlobalizationNative_GetSortKeyNative
_GlobalizationNative_GetLocaleName
_GlobalizationNative_GetLocaleInfoString
_GlobalizationNative_GetDefaultLocaleName
_GlobalizationNative_IsPredefinedLocale
_GlobalizationNative_GetLocaleTimeFormat
_GlobalizationNative_GetLocaleInfoInt
_GlobalizationNative_GetLocaleInfoGroupingSizes
_GlobalizationNative_GetLocales
_GlobalizationNative_GetDefaultLocaleNameNative
_GlobalizationNative_GetLocaleInfoStringNative
_GlobalizationNative_GetLocaleInfoIntNative
_GlobalizationNative_GetLocaleInfoPrimaryGroupingSizeNative
_GlobalizationNative_GetLocaleInfoSecondaryGroupingSizeNative
_GlobalizationNative_GetLocaleNameNative
_GlobalizationNative_GetLocalesNative
_GlobalizationNative_GetLocaleTimeFormatNative
_GlobalizationNative_IsPredefinedLocaleNative
_GlobalizationNative_GetTimeZoneDisplayName
_GlobalizationNative_GetTimeZoneDisplayNameNative
_SystemNative_GetErrNo
_SystemNative_SetErrNo
_SystemNative_GetEnviron
_SystemNative_FreeEnviron
_SystemNative_GetOSArchitecture
_SystemNative_GetNonCryptographicallySecureRandomBytes
_SystemNative_GetCryptographicallySecureRandomBytes
_SystemNative_GetSystemTimeAsTicks
_SystemNative_GetTimestamp
_SystemNative_LChflagsCanSetHiddenFlag
_SystemNative_CanGetHiddenFlag
_SystemNative_LogError
_SystemNative_LowLevelMonitor_Create
_SystemNative_LowLevelMonitor_Destroy
_SystemNative_LowLevelMonitor_Acquire
_SystemNative_LowLevelMonitor_Release
_SystemNative_LowLevelMonitor_Wait
_SystemNative_LowLevelMonitor_Signal_Release
_SystemNative_AlignedAlloc
_SystemNative_AlignedFree
_SystemNative_AlignedRealloc
_SystemNative_Calloc
_SystemNative_Free
_SystemNative_Malloc
_SystemNative_Realloc
_SystemNative_GetReadDirRBufferSize
_SystemNative_ReadDirR
_SystemNative_SchedGetCpu
_SystemNative_TryGetUInt32OSThreadId
_SystemNative_GetPid
_SystemNative_CreateAutoreleasePool
_SystemNative_DrainAutoreleasePool
_SystemNative_ChDir
_SystemNative_ChMod
_SystemNative_FChMod
_SystemNative_CopyFile
_SystemNative_GetDefaultSearchOrderPseudoHandle
_SystemNative_GetFileSystemType
_SystemNative_FLock
_SystemNative_FSync
_SystemNative_GetCpuUtilization
_SystemNative_GetCwd
_SystemNative_GetDefaultTimeZone
_SystemNative_GetEnv
_SystemNative_GetProcessPath
_SystemNative_GetUnixVersion
_SystemNative_LChflags
_SystemNative_FChflags
_SystemNative_LowLevelMonitor_TimedWait
_SystemNative_LSeek
_SystemNative_MkDir
_SystemNative_MkdTemp
_SystemNative_MksTemps
_SystemNative_Open
_SystemNative_PosixFAdvise
_SystemNative_FAllocate
_SystemNative_PRead
_SystemNative_PReadV
_SystemNative_PWrite
_SystemNative_PWriteV
_SystemNative_OpenDir
_SystemNative_CloseDir
_SystemNative_ReadLink
_SystemNative_Rename
_SystemNative_RmDir
_SystemNative_SymLink
_SystemNative_SysLog
_SystemNative_UTimensat
_SystemNative_FUTimens
_SystemNative_SearchPath
_SystemNative_SearchPath_TempDirectory
_SystemNative_iOSSupportVersion
_xamarin_CGPoint__VNNormalizedFaceBoundingBoxPointForLandmarkPoint_Vector2_CGRect_nuint_nuint_string
_xamarin_CGPoint__VNImagePointForFaceLandmarkPoint_Vector2_CGRect_nuint_nuint_string
_OBJC_CLASS_$_XamarinSwiftFunctions
_xamarin_UIApplicationMain
_xamarin_os_log
_xamarin_release_managed_ref
_xamarin_set_gchandle_with_flags_safe
_xamarin_localized_string_format
_xamarin_localized_string_format_1
_xamarin_localized_string_format_2
_xamarin_localized_string_format_3
_xamarin_localized_string_format_4
_xamarin_localized_string_format_5
_xamarin_localized_string_format_6
_xamarin_localized_string_format_7
_xamarin_localized_string_format_8
_xamarin_localized_string_format_9
_xamarin_init_nsthread
_xamarin_locate_assembly_resource
_xamarin_switch_gchandle
_xamarin_mono_object_retain
_xamarin_find_protocol_wrapper_type
_xamarin_is_user_type
_xamarin_log
_xamarin_release_block_on_main_thread
_xamarin_get_original_working_directory_path
_xamarin_get_block_descriptor
_xamarin_is_object_valid
_xamarin_free
_xamarin_simd__NMatrix3_objc_msgSend
_xamarin_simd__NMatrix3_objc_msgSendSuper
_xamarin_simd__NMatrix3_objc_msgSend_stret
_xamarin_simd__NMatrix3_objc_msgSendSuper_stret
_xamarin_simd__NMatrix4x3_objc_msgSend
_xamarin_simd__NMatrix4x3_objc_msgSendSuper
_xamarin_simd__NMatrix4x3_objc_msgSend_stret
_xamarin_simd__NMatrix4x3_objc_msgSendSuper_stret
_xamarin_NativeHandle_objc_msgSend_NativeHandle_exception
_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_exception
_xamarin_NativeHandle_objc_msgSend_NativeHandle_NativeHandle_UIntPtr_exception
_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_UIntPtr_exception
_xamarin_NativeHandle_objc_msgSend_NativeHandle_IntPtr_IntPtr_IntPtr_UIntPtr_exception
_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_IntPtr_IntPtr_UIntPtr_exception
_xamarin_NativeHandle_objc_msgSend_NativeHandle_UIntPtr_IntPtr_UIntPtr_exception
_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_IntPtr_UIntPtr_exception
_xamarin_simd__Vector2_objc_msgSend
_xamarin_simd__Vector2_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_Vector2
_xamarin_simd__void_objc_msgSendSuper_Vector2
_xamarin_simd__Vector3_objc_msgSend
_xamarin_simd__Vector3_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_Vector3
_xamarin_simd__void_objc_msgSendSuper_Vector3
_xamarin_simd__void_objc_msgSend_NMatrix3
_xamarin_simd__void_objc_msgSendSuper_NMatrix3
_xamarin_simd__NativeHandle_objc_msgSend_Vector2
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2
_xamarin_simd__NativeHandle_objc_msgSend_Vector3
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3
_xamarin_simd__NVector2i_objc_msgSend
_xamarin_simd__NVector2i_objc_msgSendSuper
_xamarin_simd__NativeHandle_objc_msgSend_NVector2i_int_int_bool
_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i_int_int_bool
_xamarin_simd__NativeHandle_objc_msgSend_NVector2i_int_int_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i_int_int_bool_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_NVector2i
_xamarin_simd__IntPtr_objc_msgSendSuper_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_NVector2i
_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_Vector2_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_Vector2_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_Vector2
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_Vector2
_xamarin_simd__GKTriangle_objc_msgSend_UIntPtr
_xamarin_simd__GKTriangle_objc_msgSendSuper_UIntPtr
_xamarin_simd__GKTriangle_objc_msgSend_stret_UIntPtr
_xamarin_simd__GKTriangle_objc_msgSendSuper_stret_UIntPtr
_xamarin_simd__float_objc_msgSend_Vector2
_xamarin_simd__float_objc_msgSendSuper_Vector2
_xamarin_simd__void_objc_msgSend_NVector3d
_xamarin_simd__void_objc_msgSendSuper_NVector3d
_xamarin_simd__NVector2d_objc_msgSend
_xamarin_simd__NVector2d_objc_msgSendSuper
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NVector2d_NVector2d_NVector2i_bool
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NVector2d_NVector2d_NVector2i_bool
_xamarin_simd__float_objc_msgSend_NVector2i
_xamarin_simd__float_objc_msgSendSuper_NVector2i
_xamarin_simd__void_objc_msgSend_float_NVector2i
_xamarin_simd__void_objc_msgSendSuper_float_NVector2i
_xamarin_simd__GKBox_objc_msgSend
_xamarin_simd__GKBox_objc_msgSendSuper
_xamarin_simd__GKBox_objc_msgSend_stret
_xamarin_simd__GKBox_objc_msgSendSuper_stret
_xamarin_simd__NativeHandle_objc_msgSend_GKBox_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_GKBox_float
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector3
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector3
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_GKBox
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_GKBox
_xamarin_simd__NativeHandle_objc_msgSend_GKBox
_xamarin_simd__NativeHandle_objc_msgSendSuper_GKBox
_xamarin_simd__Vector2_objc_msgSend_UIntPtr
_xamarin_simd__Vector2_objc_msgSendSuper_UIntPtr
_xamarin_simd__Vector3_objc_msgSend_UIntPtr
_xamarin_simd__Vector3_objc_msgSendSuper_UIntPtr
_xamarin_simd__NativeHandle_objc_msgSend_GKQuad_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_GKQuad_float
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector2_exception
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector2_exception
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_GKQuad
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_GKQuad
_xamarin_simd__NativeHandle_objc_msgSend_GKQuad
_xamarin_simd__NativeHandle_objc_msgSendSuper_GKQuad
_xamarin_simd__GKQuad_objc_msgSend
_xamarin_simd__GKQuad_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_NativeHandle_Vector2_Vector2_IntPtr
_xamarin_simd__void_objc_msgSendSuper_NativeHandle_Vector2_Vector2_IntPtr
_xamarin_simd__void_objc_msgSend_NativeHandle_Vector2_Vector2
_xamarin_simd__void_objc_msgSendSuper_NativeHandle_Vector2_Vector2
_xamarin_simd__NativeHandle_objc_msgSend_Vector2_Vector2_exception
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2_Vector2_exception
_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSend
_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSendSuper
_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSend_stret
_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSendSuper_stret
_xamarin_simd__MPSImageHistogramInfo_objc_msgSend
_xamarin_simd__MPSImageHistogramInfo_objc_msgSendSuper
_xamarin_simd__MPSImageHistogramInfo_objc_msgSend_stret
_xamarin_simd__MPSImageHistogramInfo_objc_msgSendSuper_stret
_xamarin_simd__Vector4_objc_msgSend
_xamarin_simd__Vector4_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_Vector4
_xamarin_simd__void_objc_msgSendSuper_Vector4
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_ref_MPSImageHistogramInfo
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_ref_MPSImageHistogramInfo
_xamarin_simd__void_objc_msgSend_NMatrix4_Double
_xamarin_simd__void_objc_msgSendSuper_NMatrix4_Double
_xamarin_simd__void_objc_msgSend_NMatrix4d_Double
_xamarin_simd__void_objc_msgSendSuper_NMatrix4d_Double
_xamarin_simd__NMatrix4_objc_msgSend_Double
_xamarin_simd__NMatrix4_objc_msgSendSuper_Double
_xamarin_simd__NMatrix4_objc_msgSend_stret_Double
_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_Double
_xamarin_simd__NMatrix4d_objc_msgSend_Double
_xamarin_simd__NMatrix4d_objc_msgSendSuper_Double
_xamarin_simd__NMatrix4d_objc_msgSend_stret_Double
_xamarin_simd__NMatrix4d_objc_msgSendSuper_stret_Double
_xamarin_simd__void_objc_msgSend_Quaternion_Double
_xamarin_simd__void_objc_msgSendSuper_Quaternion_Double
_xamarin_simd__void_objc_msgSend_NQuaterniond_Double
_xamarin_simd__void_objc_msgSendSuper_NQuaterniond_Double
_xamarin_simd__Quaternion_objc_msgSend_Double
_xamarin_simd__Quaternion_objc_msgSendSuper_Double
_xamarin_simd__NQuaterniond_objc_msgSend_Double
_xamarin_simd__NQuaterniond_objc_msgSendSuper_Double
_xamarin_simd__NQuaterniond_objc_msgSend_stret_Double
_xamarin_simd__NQuaterniond_objc_msgSendSuper_stret_Double
_xamarin_simd__void_objc_msgSend_Vector2_Double
_xamarin_simd__void_objc_msgSendSuper_Vector2_Double
_xamarin_simd__void_objc_msgSend_NVector2d_Double
_xamarin_simd__void_objc_msgSendSuper_NVector2d_Double
_xamarin_simd__Vector2_objc_msgSend_Double
_xamarin_simd__Vector2_objc_msgSendSuper_Double
_xamarin_simd__NVector2d_objc_msgSend_Double
_xamarin_simd__NVector2d_objc_msgSendSuper_Double
_xamarin_simd__void_objc_msgSend_NVector3_Double
_xamarin_simd__void_objc_msgSendSuper_NVector3_Double
_xamarin_simd__void_objc_msgSend_NVector3d_Double
_xamarin_simd__void_objc_msgSendSuper_NVector3d_Double
_xamarin_simd__NVector3_objc_msgSend_Double
_xamarin_simd__NVector3_objc_msgSendSuper_Double
_xamarin_simd__NVector3d_objc_msgSend_Double
_xamarin_simd__NVector3d_objc_msgSendSuper_Double
_xamarin_simd__NVector3d_objc_msgSend_stret_Double
_xamarin_simd__NVector3d_objc_msgSendSuper_stret_Double
_xamarin_simd__void_objc_msgSend_Vector4_Double
_xamarin_simd__void_objc_msgSendSuper_Vector4_Double
_xamarin_simd__void_objc_msgSend_NVector4d_Double
_xamarin_simd__void_objc_msgSendSuper_NVector4d_Double
_xamarin_simd__Vector4_objc_msgSend_Double
_xamarin_simd__Vector4_objc_msgSendSuper_Double
_xamarin_simd__NVector4d_objc_msgSend_Double
_xamarin_simd__NVector4d_objc_msgSendSuper_Double
_xamarin_simd__NVector4d_objc_msgSend_stret_Double
_xamarin_simd__NVector4d_objc_msgSendSuper_stret_Double
_xamarin_simd__NMatrix4d_objc_msgSend
_xamarin_simd__NMatrix4d_objc_msgSendSuper
_xamarin_simd__NMatrix4d_objc_msgSend_stret
_xamarin_simd__NMatrix4d_objc_msgSendSuper_stret
_xamarin_simd__void_objc_msgSend_NMatrix4d
_xamarin_simd__void_objc_msgSendSuper_NMatrix4d
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret
_xamarin_simd__NVector3_objc_msgSend
_xamarin_simd__NVector3_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_NVector3
_xamarin_simd__void_objc_msgSendSuper_NVector3
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_Double
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_Double
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret_Double
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret_Double
_xamarin_simd__NMatrix4_objc_msgSend
_xamarin_simd__NMatrix4_objc_msgSendSuper
_xamarin_simd__NMatrix4_objc_msgSend_stret
_xamarin_simd__NMatrix4_objc_msgSendSuper_stret
_xamarin_simd__void_objc_msgSend_MDLAxisAlignedBoundingBox_bool
_xamarin_simd__void_objc_msgSendSuper_MDLAxisAlignedBoundingBox_bool
_xamarin_simd__void_objc_msgSend_Vector3_Vector3
_xamarin_simd__void_objc_msgSendSuper_Vector3_Vector3
_xamarin_simd__Vector3_objc_msgSend_NVector2i_NVector2i
_xamarin_simd__Vector3_objc_msgSendSuper_NVector2i_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_bool_NativeHandle_NVector2i_IntPtr_UIntPtr_IntPtr_bool
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_bool_NativeHandle_NVector2i_IntPtr_UIntPtr_IntPtr_bool
_xamarin_simd__NativeHandle_objc_msgSend_float_NativeHandle_NVector2i_int_IntPtr_NativeHandle_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_NativeHandle_NVector2i_int_IntPtr_NativeHandle_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_float_float_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_float_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_Vector3_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_NativeHandle
_xamarin_simd__void_objc_msgSend_NMatrix4
_xamarin_simd__void_objc_msgSendSuper_NMatrix4
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector2
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector2
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector3
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector3
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector4
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector4
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_NMatrix4
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_NMatrix4
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector3i_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector3i_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_bool_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_bool_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_int_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_int_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_Vector3_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_bool_IntPtr_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_NativeHandle_int_UInt32_NativeHandle
_xamarin_simd__IntPtr_objc_msgSendSuper_NativeHandle_int_UInt32_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_Vector3_NVector3i_IntPtr_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_NVector3i_IntPtr_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_Vector2_NVector2i_IntPtr_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2_NVector2i_IntPtr_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_Vector3_UIntPtr_UIntPtr_IntPtr_bool_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_UIntPtr_UIntPtr_IntPtr_bool_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_UIntPtr_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_UIntPtr_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle
_xamarin_simd__bool_objc_msgSend_NVector2i_IntPtr_float_NativeHandle_NativeHandle_NativeHandle
_xamarin_simd__bool_objc_msgSendSuper_NVector2i_IntPtr_float_NativeHandle_NativeHandle_NativeHandle
_xamarin_simd__bool_objc_msgSend_NVector2i_NativeHandle_NativeHandle_NativeHandle_NativeHandle
_xamarin_simd__bool_objc_msgSendSuper_NVector2i_NativeHandle_NativeHandle_NativeHandle_NativeHandle
_xamarin_simd__IntPtr_objc_msgSend_float_NativeHandle_NVector2i_IntPtr
_xamarin_simd__IntPtr_objc_msgSendSuper_float_NativeHandle_NVector2i_IntPtr
_xamarin_simd__NativeHandle_objc_msgSend_float_NativeHandle_NVector2i_int_IntPtr_bool
_xamarin_simd__NativeHandle_objc_msgSendSuper_float_NativeHandle_NVector2i_int_IntPtr_bool
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_IntPtr_NVector2i_float_float_float_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_NVector2i_float_float_float_float
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_IntPtr_NVector2i_float_float_float_float_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_NVector2i_float_float_float_float_float
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NVector2i
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NVector2i_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NVector2i_float
_xamarin_simd__NativeHandle_objc_msgSend_NMatrix4
_xamarin_simd__NativeHandle_objc_msgSendSuper_NMatrix4
_xamarin_simd__NativeHandle_objc_msgSend_NMatrix4_bool
_xamarin_simd__NativeHandle_objc_msgSendSuper_NMatrix4_bool
_xamarin_simd__Vector3_objc_msgSend_Double
_xamarin_simd__Vector3_objc_msgSendSuper_Double
_xamarin_simd__void_objc_msgSend_Vector3_Double
_xamarin_simd__void_objc_msgSendSuper_Vector3_Double
_xamarin_simd__NMatrix4_objc_msgSend_NativeHandle_Double
_xamarin_simd__NMatrix4_objc_msgSendSuper_NativeHandle_Double
_xamarin_simd__NMatrix4_objc_msgSend_stret_NativeHandle_Double
_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_NativeHandle_Double
_xamarin_simd__MDLVoxelIndexExtent_objc_msgSend
_xamarin_simd__MDLVoxelIndexExtent_objc_msgSendSuper
_xamarin_simd__MDLVoxelIndexExtent_objc_msgSend_stret
_xamarin_simd__MDLVoxelIndexExtent_objc_msgSendSuper_stret
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_MDLAxisAlignedBoundingBox_float
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_MDLAxisAlignedBoundingBox_float
_xamarin_simd__bool_objc_msgSend_NVector4i_bool_bool_bool_bool
_xamarin_simd__bool_objc_msgSendSuper_NVector4i_bool_bool_bool_bool
_xamarin_simd__void_objc_msgSend_NVector4i
_xamarin_simd__void_objc_msgSendSuper_NVector4i
_xamarin_simd__NativeHandle_objc_msgSend_MDLVoxelIndexExtent
_xamarin_simd__NativeHandle_objc_msgSendSuper_MDLVoxelIndexExtent
_xamarin_simd__NVector4i_objc_msgSend_Vector3
_xamarin_simd__NVector4i_objc_msgSendSuper_Vector3
_xamarin_simd__Vector3_objc_msgSend_NVector4i
_xamarin_simd__Vector3_objc_msgSendSuper_NVector4i
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_NVector4i
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_NVector4i
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret_NVector4i
_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret_NVector4i
_xamarin_simd__NMatrix4_objc_msgSend_NativeHandle
_xamarin_simd__NMatrix4_objc_msgSendSuper_NativeHandle
_xamarin_simd__NMatrix4_objc_msgSend_stret_NativeHandle
_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_NativeHandle
_xamarin_simd__Quaternion_objc_msgSend
_xamarin_simd__Quaternion_objc_msgSendSuper
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Quaternion_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Quaternion_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Quaternion
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Quaternion
_xamarin_simd__NativeHandle_objc_msgSend_NVector2d_NativeHandle
_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2d_NativeHandle
_xamarin_simd__void_objc_msgSend_NVector2d
_xamarin_simd__void_objc_msgSendSuper_NVector2d
_xamarin_simd__NativeHandle_objc_msgSend_NVector2d_IntPtr
_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2d_IntPtr
_xamarin_simd__Vector3_objc_msgSend_Vector3
_xamarin_simd__Vector3_objc_msgSendSuper_Vector3
_xamarin_simd__NativeHandle_objc_msgSend_Vector4
_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector4
_xamarin_vector_float3__Vector4_objc_msgSend
_xamarin_vector_float3__Vector4_objc_msgSendSuper
_xamarin_vector_float3__void_objc_msgSend_Vector4
_xamarin_vector_float3__void_objc_msgSendSuper_Vector4
_xamarin_simd__void_objc_msgSend_Quaternion
_xamarin_simd__void_objc_msgSendSuper_Quaternion
_xamarin_simd__NMatrix2_objc_msgSend
_xamarin_simd__NMatrix2_objc_msgSendSuper
_xamarin_simd__void_objc_msgSend_NMatrix2
_xamarin_simd__void_objc_msgSendSuper_NMatrix2
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector2
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector2
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector4
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector4
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix2
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix2
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix3
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix3
_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix4
_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix4
_xamarin_simd__Vector2_objc_msgSend_IntPtr
_xamarin_simd__Vector2_objc_msgSendSuper_IntPtr
