{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/PetSitterConnect.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/PetSitterConnect.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/PetSitterConnect.csproj", "projectName": "PetSitterConnect", "projectPath": "/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/PetSitterConnect.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/obj/", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0-android", "net9.0-ios", "net9.0-maccatalyst"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "projectReferences": {}}, "net9.0-ios18.5": {"targetAlias": "net9.0-ios", "projectReferences": {}}, "net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[12.0.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.70, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.Android": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-ios18.5": {"targetAlias": "net9.0-ios", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[12.0.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.70, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.iOS": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[12.0.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.70, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"android-arm64": {"#import": []}, "android-x64": {"#import": []}, "iossimulator-arm64": {"#import": []}, "maccatalyst-arm64": {"#import": []}}}}}