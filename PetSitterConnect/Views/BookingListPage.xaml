<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.BookingListPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:PetSitterConnect.ViewModels"
             xmlns:models="clr-namespace:PetSitterConnect.Models"
             x:DataType="viewmodels:BookingListViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,Auto,Auto,*">

        <!-- User Role Header -->
        <Border Grid.Row="0"
                BackgroundColor="{Binding UserRoleColor}"
                StrokeThickness="0"
                Padding="15,10">
            <Grid ColumnDefinitions="Auto,*,Auto">
                <Label Grid.Column="0"
                       Text="{Binding UserRoleIcon}"
                       FontSize="20"
                       VerticalOptions="Center" />
                <StackLayout Grid.Column="1"
                             Orientation="Vertical"
                             Spacing="2"
                             Margin="10,0,0,0">
                    <Label Text="{Binding CurrentUser.FullName}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="White" />
                    <Label Text="{Binding ShowAsSitter, Converter={StaticResource BoolToBookingModeConverter}}"
                           FontSize="12"
                           TextColor="White"
                           Opacity="0.9" />
                </StackLayout>
                <Border Grid.Column="2"
                        BackgroundColor="White"
                        StrokeThickness="0"
                        Padding="8,4"
                        StrokeShape="RoundRectangle 12">
                    <Label Text="{Binding UserRoleLabel}"
                           FontSize="10"
                           FontAttributes="Bold"
                           TextColor="{Binding UserRoleColor}" />
                </Border>
            </Grid>
        </Border>

        <!-- Header Controls -->
        <StackLayout Grid.Row="1" Padding="15" Spacing="10">
            
            <!-- View Toggle and Filter -->
            <Grid ColumnDefinitions="*,Auto">
                
                <!-- Filter Picker -->
                <Picker Grid.Column="0"
                        ItemsSource="{Binding FilterOptions}"
                        SelectedItem="{Binding SelectedFilter}"
                        Title="Filter bookings" />
                
                <!-- Toggle View Mode -->
                <Button Grid.Column="1"
                        Text="{Binding ShowAsSitter, Converter={StaticResource BoolToTextConverter}}"
                        Command="{Binding ToggleViewModeCommand}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource SecondaryDark}}"
                        TextColor="White"
                        FontSize="12"
                        Margin="10,0,0,0" />
            </Grid>
        </StackLayout>

        <!-- Results Count -->
        <Label Grid.Row="2"
               Text="{Binding FilteredBookings.Count, StringFormat='{0} bookings found'}"
               FontSize="14"
               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}"
               Margin="15,0,15,10" />

        <!-- Bookings List -->
        <RefreshView Grid.Row="3"
                     IsRefreshing="{Binding IsRefreshing}"
                     Command="{Binding RefreshCommand}">
            
            <CollectionView ItemsSource="{Binding FilteredBookings}"
                            SelectionMode="None">
                
                <CollectionView.EmptyView>
                    <StackLayout HorizontalOptions="Center" VerticalOptions="Center" Spacing="10">
                        <Label Text="No bookings found"
                               FontSize="18"
                               FontAttributes="Bold"
                               HorizontalOptions="Center" />
                        <Label Text="Your bookings will appear here"
                               FontSize="14"
                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}"
                               HorizontalOptions="Center" />
                    </StackLayout>
                </CollectionView.EmptyView>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:Booking">
                        <Grid Padding="15" RowDefinitions="Auto">
                            
                            <Frame Grid.Row="0"
                                   BackgroundColor="{AppThemeBinding Light=White, Dark={StaticResource Gray900}}"
                                   BorderColor="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray700}}"
                                   CornerRadius="10"
                                   HasShadow="True"
                                   Margin="0,5">
                                
                                <Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto" Padding="15">
                                    
                                    <!-- Header: Pet Name and Status -->
                                    <Grid Grid.Row="0" ColumnDefinitions="*,Auto">
                                        <Label Grid.Column="0"
                                               Text="{Binding PetCareRequest.Pet.Name}"
                                               FontSize="18"
                                               FontAttributes="Bold"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark=White}" />
                                        
                                        <Label Grid.Column="1"
                                               Text="{Binding Status}"
                                               FontSize="12"
                                               FontAttributes="Bold"
                                               TextColor="White"
                                               BackgroundColor="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                               Padding="8,4"
                                               HorizontalOptions="End" />
                                    </Grid>
                                    
                                    <!-- Request Title -->
                                    <Label Grid.Row="1"
                                           Text="{Binding PetCareRequest.Title}"
                                           FontSize="16"
                                           TextColor="{AppThemeBinding Light={StaticResource Gray700}, Dark={StaticResource Gray200}}"
                                           Margin="0,5,0,0" />
                                    
                                    <!-- Dates and Duration -->
                                    <StackLayout Grid.Row="2" Orientation="Horizontal" Spacing="10" Margin="0,5">
                                        <Label Text="{Binding PetCareRequest.StartDate, StringFormat='{0:MMM dd, yyyy}'}"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                                        <Label Text="-"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                                        <Label Text="{Binding PetCareRequest.EndDate, StringFormat='{0:MMM dd, yyyy}'}"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                                        <Label Text="{Binding PetCareRequest.DurationInDays, StringFormat='({0} days)'}"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                                    </StackLayout>
                                    
                                    <!-- Amount and Care Type -->
                                    <Grid Grid.Row="3" ColumnDefinitions="*,Auto" Margin="0,5">
                                        <Label Grid.Column="0"
                                               Text="{Binding PetCareRequest.CareType}"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource SecondaryDark}}" />
                                        
                                        <Label Grid.Column="1"
                                               Text="{Binding TotalAmount, StringFormat='${0:F2}'}"
                                               FontSize="16"
                                               FontAttributes="Bold"
                                               TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                                    </Grid>
                                    
                                    <!-- Action Buttons -->
                                    <StackLayout Grid.Row="4" Orientation="Horizontal" Spacing="10" Margin="0,10,0,0"
                                                 IsVisible="{Binding Status, Converter={StaticResource StatusToActionsVisibleConverter}}">
                                        
                                        <!-- Accept Button (for sitters) -->
                                        <Button Text="Accept"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:BookingListViewModel}}, Path=AcceptBookingCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="Green"
                                                TextColor="White"
                                                FontSize="12"
                                                IsVisible="{Binding Status, Converter={StaticResource StatusToPendingConverter}}" />
                                        
                                        <!-- Reject Button (for sitters) -->
                                        <Button Text="Reject"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:BookingListViewModel}}, Path=RejectBookingCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="Red"
                                                TextColor="White"
                                                FontSize="12"
                                                IsVisible="{Binding Status, Converter={StaticResource StatusToPendingConverter}}" />
                                        
                                        <!-- Cancel Button -->
                                        <Button Text="Cancel"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:BookingListViewModel}}, Path=CancelBookingCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="Orange"
                                                TextColor="White"
                                                FontSize="12"
                                                IsVisible="{Binding CanBeCancelled}" />
                                        
                                        <!-- Complete Button (for sitters) -->
                                        <Button Text="Complete"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:BookingListViewModel}}, Path=CompleteBookingCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="DarkGreen"
                                                TextColor="White"
                                                FontSize="12"
                                                IsVisible="{Binding CanBeCompleted}" />
                                    </StackLayout>
                                </Grid>
                            </Frame>
                            
                            <!-- Tap Gesture -->
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:BookingListViewModel}}, Path=ViewBookingDetailsCommand}"
                                                      CommandParameter="{Binding .}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

        <!-- Loading Indicator -->
        <ActivityIndicator Grid.Row="3"
                           IsVisible="{Binding IsBusy}"
                           IsRunning="{Binding IsBusy}"
                           Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
    </Grid>

</ContentPage>
