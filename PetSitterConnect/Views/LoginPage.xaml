<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:PetSitterConnect.ViewModels"
             x:DataType="viewmodels:LoginViewModel"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,40,0,40">
                <Image Source="dotnet_bot.png" 
                       HeightRequest="100" 
                       WidthRequest="100"
                       HorizontalOptions="Center" />
                <Label Text="PetSitter Connect" 
                       FontSize="28" 
                       FontAttributes="Bold"
                       HorizontalOptions="Center"
                       TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                <Label Text="Welcome back! Please sign in to your account." 
                       FontSize="16" 
                       HorizontalOptions="Center"
                       TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
            </StackLayout>

            <!-- Login Form -->
            <StackLayout Grid.Row="1" Spacing="20">
                
                <!-- Email Entry -->
                <StackLayout Spacing="5">
                    <Label Text="Email" FontSize="16" FontAttributes="Bold" />
                    <Entry Text="{Binding Email}" 
                           Placeholder="Enter your email"
                           Keyboard="Email"
                           ReturnType="Next" />
                </StackLayout>

                <!-- Password Entry -->
                <StackLayout Spacing="5">
                    <Label Text="Password" FontSize="16" FontAttributes="Bold" />
                    <Entry Text="{Binding Password}" 
                           Placeholder="Enter your password"
                           IsPassword="True"
                           ReturnType="Done" />
                </StackLayout>

                <!-- Remember Me -->
                <StackLayout Orientation="Horizontal" Spacing="10">
                    <CheckBox IsChecked="{Binding RememberMe}" />
                    <Label Text="Remember me" VerticalOptions="Center" />
                </StackLayout>

                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}" 
                       TextColor="Red" 
                       FontSize="14"
                       IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" />

                <!-- Login Button -->
                <Button Text="Sign In" 
                        Command="{Binding LoginCommand}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                        TextColor="White"
                        FontSize="18"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        CornerRadius="25"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Activity Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}"
                                   Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />

                <!-- Forgot Password -->
                <Button Text="Forgot Password?" 
                        Command="{Binding ForgotPasswordCommand}"
                        BackgroundColor="Transparent"
                        TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                        FontSize="14" />

            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" Spacing="10" Margin="0,20,0,0">
                <BoxView HeightRequest="1" 
                         BackgroundColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                
                <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Spacing="5">
                    <Label Text="Don't have an account?" 
                           FontSize="14"
                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                    <Button Text="Sign Up" 
                            Command="{Binding NavigateToRegisterCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                            FontSize="14"
                            FontAttributes="Bold"
                            Padding="0" />
                </StackLayout>
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
