<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.RegisterPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:PetSitterConnect.ViewModels"
             xmlns:models="clr-namespace:PetSitterConnect.Models"
             x:DataType="viewmodels:RegisterViewModel"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,20,0,30">
                <Image Source="dotnet_bot.png" 
                       HeightRequest="80" 
                       WidthRequest="80"
                       HorizontalOptions="Center" />
                <Label Text="Join PetSitter Connect" 
                       FontSize="24" 
                       FontAttributes="Bold"
                       HorizontalOptions="Center"
                       TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                <Label Text="Create your account to get started" 
                       FontSize="14" 
                       HorizontalOptions="Center"
                       TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
            </StackLayout>

            <!-- Registration Form -->
            <StackLayout Grid.Row="1" Spacing="15">
                
                <!-- Personal Information -->
                <Label Text="Personal Information" 
                       FontSize="18" 
                       FontAttributes="Bold"
                       TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />

                <!-- Name Fields -->
                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0" Spacing="5">
                        <Label Text="First Name" FontSize="14" FontAttributes="Bold" />
                        <Entry Text="{Binding FirstName}" 
                               Placeholder="First name" />
                    </StackLayout>
                    <StackLayout Grid.Column="2" Spacing="5">
                        <Label Text="Last Name" FontSize="14" FontAttributes="Bold" />
                        <Entry Text="{Binding LastName}" 
                               Placeholder="Last name" />
                    </StackLayout>
                </Grid>

                <!-- Email -->
                <StackLayout Spacing="5">
                    <Label Text="Email" FontSize="14" FontAttributes="Bold" />
                    <Entry Text="{Binding Email}" 
                           Placeholder="Enter your email"
                           Keyboard="Email" />
                </StackLayout>

                <!-- Password Fields -->
                <StackLayout Spacing="5">
                    <Label Text="Password" FontSize="14" FontAttributes="Bold" />
                    <Entry Text="{Binding Password}" 
                           Placeholder="Create a password"
                           IsPassword="True" />
                </StackLayout>

                <StackLayout Spacing="5">
                    <Label Text="Confirm Password" FontSize="14" FontAttributes="Bold" />
                    <Entry Text="{Binding ConfirmPassword}" 
                           Placeholder="Confirm your password"
                           IsPassword="True" />
                </StackLayout>

                <!-- User Type -->
                <StackLayout Spacing="5">
                    <Label Text="I am a..." FontSize="14" FontAttributes="Bold" />
                    <Picker ItemsSource="{Binding UserTypes}"
                            SelectedItem="{Binding UserType}"
                            Title="Select your role" />
                </StackLayout>

                <!-- Contact Information -->
                <Label Text="Contact Information (Optional)" 
                       FontSize="18" 
                       FontAttributes="Bold"
                       TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                       Margin="0,20,0,0" />

                <!-- Phone -->
                <StackLayout Spacing="5">
                    <Label Text="Phone Number" FontSize="14" />
                    <Entry Text="{Binding PhoneNumber}" 
                           Placeholder="Phone number"
                           Keyboard="Telephone" />
                </StackLayout>

                <!-- Address -->
                <StackLayout Spacing="5">
                    <Label Text="Address" FontSize="14" />
                    <Entry Text="{Binding Address}" 
                           Placeholder="Street address" />
                </StackLayout>

                <!-- City and Postal Code -->
                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0" Spacing="5">
                        <Label Text="City" FontSize="14" />
                        <Entry Text="{Binding City}" 
                               Placeholder="City" />
                    </StackLayout>
                    <StackLayout Grid.Column="2" Spacing="5">
                        <Label Text="Postal Code" FontSize="14" />
                        <Entry Text="{Binding PostalCode}" 
                               Placeholder="Postal code" />
                    </StackLayout>
                </Grid>

                <!-- Country -->
                <StackLayout Spacing="5">
                    <Label Text="Country" FontSize="14" />
                    <Entry Text="{Binding Country}" 
                           Placeholder="Country" />
                </StackLayout>

                <!-- Terms and Conditions -->
                <StackLayout Orientation="Horizontal" Spacing="10" Margin="0,20,0,0">
                    <CheckBox IsChecked="{Binding AcceptTerms}" />
                    <Label Text="I agree to the Terms and Conditions and Privacy Policy" 
                           VerticalOptions="Center" 
                           FontSize="12" />
                </StackLayout>

                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}" 
                       TextColor="Red" 
                       FontSize="14"
                       IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" />

                <!-- Register Button -->
                <Button Text="Create Account" 
                        Command="{Binding RegisterCommand}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                        TextColor="White"
                        FontSize="18"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        CornerRadius="25"
                        Margin="0,20,0,0"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Activity Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}"
                                   Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />

            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" Spacing="10" Margin="0,20,0,0">
                <BoxView HeightRequest="1" 
                         BackgroundColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                
                <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Spacing="5">
                    <Label Text="Already have an account?" 
                           FontSize="14"
                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                    <Button Text="Sign In" 
                            Command="{Binding NavigateToLoginCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                            FontSize="14"
                            FontAttributes="Bold"
                            Padding="0" />
                </StackLayout>
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
