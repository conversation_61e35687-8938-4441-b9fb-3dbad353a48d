<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="PetSitterConnect.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:PetSitterConnect"
    xmlns:views="clr-namespace:PetSitterConnect.Views"
    Title="PetSitterConnect">

    <!-- Authentication Pages -->
    <ShellContent
        Title="Login"
        ContentTemplate="{DataTemplate views:LoginPage}"
        Route="login" />

    <ShellContent
        Title="Register"
        ContentTemplate="{DataTemplate views:RegisterPage}"
        Route="register" />

    <!-- Main App Pages -->
    <TabBar Route="main">
        <ShellContent
            Title="Home"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="home"
            Icon="home.png" />

        <ShellContent
            Title="Requests"
            ContentTemplate="{DataTemplate views:PetCareRequestListPage}"
            Route="requests"
            Icon="list.png" />

        <ShellContent
            Title="Bookings"
            ContentTemplate="{DataTemplate views:BookingListPage}"
            Route="bookings"
            Icon="calendar.png" />
    </TabBar>

</Shell>
