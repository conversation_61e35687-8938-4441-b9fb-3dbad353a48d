﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:PetSitterConnect"
             xmlns:converters="clr-namespace:PetSitterConnect.Converters"
             x:Class="PetSitterConnect.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:BoolToTextConverter x:Key="BoolToTextConverter" />
            <converters:StatusToColorConverter x:Key="StatusToColorConverter" />
            <converters:StatusToPendingConverter x:Key="StatusToPendingConverter" />
            <converters:StatusToActionsVisibleConverter x:Key="StatusToActionsVisibleConverter" />
            <converters:CountToBoolConverter x:Key="CountToBoolConverter" />
            <converters:CountToInvertedBoolConverter x:Key="CountToInvertedBoolConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
