using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using PetSitterConnect.Data;
using PetSitterConnect.Models;
using PetSitterConnect.Services;
using PetSitterConnect.ViewModels;
using PetSitterConnect.Views;
using CommunityToolkit.Maui;

namespace PetSitterConnect;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.UseMauiCommunityToolkit()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});

		// Configure Entity Framework
		var dbPath = Path.Combine(FileSystem.AppDataDirectory, "petsitter.db");
		builder.Services.AddDbContext<PetSitterDbContext>(options =>
			options.UseSqlite($"Data Source={dbPath}"));

		// Configure Identity
		builder.Services.AddIdentity<User, IdentityRole>(options =>
		{
			// Password settings
			options.Password.RequireDigit = true;
			options.Password.RequireLowercase = true;
			options.Password.RequireNonAlphanumeric = false;
			options.Password.RequireUppercase = true;
			options.Password.RequiredLength = 6;
			options.Password.RequiredUniqueChars = 1;

			// Lockout settings
			options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
			options.Lockout.MaxFailedAccessAttempts = 5;
			options.Lockout.AllowedForNewUsers = true;

			// User settings
			options.User.AllowedUserNameCharacters =
				"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
			options.User.RequireUniqueEmail = true;
		})
		.AddEntityFrameworkStores<PetSitterDbContext>()
		.AddDefaultTokenProviders();

		// Register services
		builder.Services.AddScoped<IAuthService, AuthService>();
		builder.Services.AddScoped<IUserService, UserService>();
		builder.Services.AddScoped<IPetService, PetService>();
		builder.Services.AddScoped<IPetCareRequestService, PetCareRequestService>();
		builder.Services.AddScoped<IBookingService, BookingService>();

		// Register ViewModels
		builder.Services.AddTransient<LoginViewModel>();
		builder.Services.AddTransient<RegisterViewModel>();

		// Register Views
		// TODO: Add Views here

#if DEBUG
		builder.Logging.AddDebug();
#endif

		return builder.Build();
	}
}
